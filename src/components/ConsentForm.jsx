import { useState, useRef } from 'react'
import SignatureCanvas from 'react-signature-canvas'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import './ConsentForm.css'

const ConsentForm = () => {
  const [participantName, setParticipantName] = useState('')
  const [date, setDate] = useState(new Date().toISOString().split('T')[0])
  const [isSigned, setIsSigned] = useState(false)
  const sigCanvas = useRef({})
  const formRef = useRef()

  const clearSignature = () => {
    sigCanvas.current.clear()
    setIsSigned(false)
  }

  const handleSignatureEnd = () => {
    setIsSigned(!sigCanvas.current.isEmpty())
  }

  const generatePDF = async () => {
    if (!participantName.trim()) {
      alert('Please enter participant name')
      return
    }
    
    if (!isSigned) {
      alert('Please provide your signature')
      return
    }

    try {
      // Hide the form controls during PDF generation
      const controls = document.querySelectorAll('.form-controls')
      controls.forEach(control => control.style.display = 'none')

      // Capture the form as canvas
      const canvas = await html2canvas(formRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })

      // Show the controls again
      controls.forEach(control => control.style.display = 'block')

      // Create PDF
      const pdf = new jsPDF('p', 'mm', 'a4')
      const imgData = canvas.toDataURL('image/png')
      
      const pdfWidth = pdf.internal.pageSize.getWidth()
      const pdfHeight = (canvas.height * pdfWidth) / canvas.width
      
      pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight)
      
      // Save the PDF
      const fileName = `GENESIS_Consent_${participantName.replace(/\s+/g, '_')}_${date}.pdf`
      pdf.save(fileName)
      
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Error generating PDF. Please try again.')
    }
  }

  return (
    <div className="consent-container">
      <div ref={formRef} className="consent-form">
        <header className="form-header">
          <h1>GENESIS Program Consent and Acknowledgment</h1>
          <p className="subtitle">
            Thank you for agreeing to enroll in <strong>GENESIS</strong>, a program of{' '}
            <strong>Roseman Medical Group</strong> and{' '}
            <strong>Roseman University College of Medicine</strong>. This form outlines the program details and its limitations.
          </p>
        </header>

        <section className="form-section">
          <h2>Authorization</h2>
          <p>
            I authorize <strong>Roseman Medical Group</strong>, <strong>GENESIS</strong>, and{' '}
            <strong>Roseman University of Health Sciences</strong> to assist me and members of my household 
            in addressing urgent social needs by helping us access social services.
          </p>
        </section>

        <section className="form-section">
          <h2>Understanding of Services</h2>
          <ul>
            <li>I understand that <strong>social needs</strong> include help with food, transportation, health insurance, or housing.</li>
            <li>I understand that <strong>GENESIS</strong> and <strong>Roseman Medical Group</strong> do not directly provide these services, but will help connect me with organizations that can.</li>
            <li>I understand that <strong>GENESIS</strong> operates on behalf of <strong>Roseman Medical Group</strong> when assisting with social needs.</li>
            <li>I understand that assistance may be provided:
              <ul>
                <li>At my home</li>
                <li>At the Roseman Medical Group office</li>
                <li>At the location of a third-party service provider</li>
              </ul>
            </li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Medical Services</h2>
          <ul>
            <li>I understand that <strong>GENESIS</strong> may also help me find <strong>medical services</strong>, which may be provided by Roseman Medical Group or another local healthcare provider.</li>
            <li>I understand that any third-party services will have their own <strong>independent rules and fees</strong>.</li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Home Visits</h2>
          <ul>
            <li>I understand that <strong>home visits</strong> cannot begin until I have successfully completed a <strong>home environment assessment</strong>, which includes disclosing any <strong>past criminal history</strong> by household members.</li>
            <li>I understand that past criminal history <strong>does not automatically exclude</strong> me from participating in GENESIS.</li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Legal Reporting Requirements</h2>
          <ul>
            <li>In accordance with <strong>Nevada Revised Statute (NRS) Chapter 432B</strong>, any person with reasonable cause to believe that child abuse is occurring <strong>must report</strong> to Child Protective Services (CPS) or law enforcement.<br />
            👉 GENESIS staff are <strong>mandated reporters</strong> of suspected child abuse.</li>
            <li>Under <strong>NRS 200.5092</strong>, any professional who suspects abuse or neglect of an <strong>elderly person</strong> (age 60+) or vulnerable adult is <strong>legally required to report it</strong>.<br />
            👉 GENESIS staff are <strong>mandated reporters</strong> of suspected elder abuse.</li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Limitations and Privacy</h2>
          <ul>
            <li>I understand that the success of services depends on <strong>my own participation and effort</strong>.</li>
            <li>I acknowledge that there are <strong>no guarantees</strong> that GENESIS will be successful in securing assistance.</li>
            <li>I understand that <strong>information collected</strong> by GENESIS may be used for <strong>program evaluation</strong>.
              <ul>
                <li>No report or data summary will <strong>specifically identify me or my household</strong>.</li>
              </ul>
            </li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Contact and Complaints</h2>
          <div className="contact-box">
            <p><em>Service excellence is our top priority. If you have a complaint about the service you've received, we take it seriously and are committed to resolving it.</em></p>
            <p>To file a formal complaint, please contact:<br />
            📞 <strong>************</strong></p>
          </div>
        </section>

        <section className="signature-section">
          <div className="participant-info">
            <div className="input-group">
              <label htmlFor="participantName">Participant Name:</label>
              <input
                type="text"
                id="participantName"
                value={participantName}
                onChange={(e) => setParticipantName(e.target.value)}
                placeholder="Enter full name"
                required
              />
            </div>
            <div className="input-group">
              <label htmlFor="date">Date:</label>
              <input
                type="date"
                id="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                required
              />
            </div>
          </div>

          <div className="signature-area">
            <label>Participant Signature:</label>
            <div className="signature-canvas-container">
              <SignatureCanvas
                ref={sigCanvas}
                penColor="black"
                canvasProps={{
                  width: 500,
                  height: 200,
                  className: 'signature-canvas'
                }}
                onEnd={handleSignatureEnd}
              />
            </div>
            <div className="signature-line">
              <span>Signature</span>
            </div>
          </div>
        </section>
      </div>

      <div className="form-controls">
        <button onClick={clearSignature} className="btn btn-secondary">
          Clear Signature
        </button>
        <button onClick={generatePDF} className="btn btn-primary">
          Generate PDF
        </button>
      </div>
    </div>
  )
}

export default ConsentForm
