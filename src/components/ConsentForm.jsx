import { useState, useRef } from 'react'
import SignatureCanvas from 'react-signature-canvas'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import './ConsentForm.css'

const ConsentForm = () => {
  const [participantName, setParticipantName] = useState('')
  const [date, setDate] = useState(new Date().toISOString().split('T')[0])
  const [isSigned, setIsSigned] = useState(false)
  const sigCanvas = useRef({})
  const formRef = useRef()

  const clearSignature = () => {
    sigCanvas.current.clear()
    setIsSigned(false)
  }

  const handleSignatureEnd = () => {
    setIsSigned(!sigCanvas.current.isEmpty())
  }

  const findSignaturePosition = () => {
    const signatureContainer = document.querySelector('.signature-canvas-container')
    const formRect = formRef.current.getBoundingClientRect()
    const sigRect = signatureContainer.getBoundingClientRect()

    // Calculate relative position within the form
    const relativeTop = sigRect.top - formRect.top
    const relativeLeft = sigRect.left - formRect.left

    console.log('Signature container position:', {
      relativeTop,
      relativeLeft,
      formHeight: formRect.height,
      formWidth: formRect.width
    })

    return { relativeTop, relativeLeft }
  }

  const createSignatureImage = () => {
    // Create a clean signature image with white background
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    // Set canvas size to match signature canvas
    canvas.width = 500
    canvas.height = 200

    // Fill with white background
    ctx.fillStyle = 'white'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Get the signature data and draw it on the clean canvas
    const signatureData = sigCanvas.current.toDataURL()

    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        ctx.drawImage(img, 0, 0)
        resolve(canvas.toDataURL('image/png'))
      }
      img.src = signatureData
    })
  }

  const generatePDF = async () => {
    if (!participantName.trim()) {
      alert('Please enter participant name')
      return
    }

    if (!isSigned) {
      alert('Please provide your signature')
      return
    }

    try {
      // Create clean signature image
      const signatureImageData = await createSignatureImage()

      // Hide form controls and signature canvas during capture
      const controls = document.querySelectorAll('.form-controls')
      const signatureCanvas = document.querySelector('.signature-canvas')

      controls.forEach(control => control.style.display = 'none')
      signatureCanvas.style.visibility = 'hidden' // Hide but keep space

      // Capture the form
      const canvas = await html2canvas(formRef.current, {
        scale: 1.5,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false
      })

      // Show controls and signature canvas again
      controls.forEach(control => control.style.display = 'block')
      signatureCanvas.style.visibility = 'visible'

      // Create PDF
      const pdf = new jsPDF('p', 'mm', 'a4')

      // Add the form image to PDF
      const formImgData = canvas.toDataURL('image/png')
      const pdfWidth = pdf.internal.pageSize.getWidth()
      const pdfHeight = (canvas.height * pdfWidth) / canvas.width

      // Add form to PDF (handle multiple pages if needed)
      const pageHeight = pdf.internal.pageSize.getHeight()

      if (pdfHeight <= pageHeight) {
        pdf.addImage(formImgData, 'PNG', 0, 0, pdfWidth, pdfHeight)
      } else {
        let heightLeft = pdfHeight
        let position = 0
        pdf.addImage(formImgData, 'PNG', 0, position, pdfWidth, pdfHeight)
        heightLeft -= pageHeight
        while (heightLeft >= 0) {
          position = heightLeft - pdfHeight
          pdf.addPage()
          pdf.addImage(formImgData, 'PNG', 0, position, pdfWidth, pdfHeight)
          heightLeft -= pageHeight
        }
      }

      // Calculate signature position in the signature box
      const { relativeTop, relativeLeft } = findSignaturePosition()

      // Convert to PDF coordinates (mm)
      const scale = 1.5
      const formWidthPx = formRef.current.offsetWidth
      const formHeightPx = formRef.current.offsetHeight

      // Calculate the ratio between PDF size and captured canvas size
      const pdfWidthMm = pdfWidth
      const canvasWidthPx = formWidthPx * scale
      const mmPerPx = pdfWidthMm / canvasWidthPx

      const signatureX = relativeLeft * mmPerPx
      const signatureY = relativeTop * mmPerPx
      const signatureWidth = 500 * mmPerPx  // Canvas width in pixels
      const signatureHeight = 200 * mmPerPx // Canvas height in pixels

      console.log('Signature positioning details:', {
        formWidthPx,
        formHeightPx,
        canvasWidthPx,
        pdfWidthMm,
        mmPerPx,
        relativeTop,
        relativeLeft,
        signatureX,
        signatureY,
        signatureWidth,
        signatureHeight,
        pdfHeight
      })

      // Ensure signature is within PDF bounds
      const maxX = pdfWidth - signatureWidth
      const maxY = pdfHeight - signatureHeight
      const finalX = Math.max(0, Math.min(signatureX, maxX))
      const finalY = Math.max(0, Math.min(signatureY, maxY))

      console.log('Final signature position:', { finalX, finalY })

      // Add the signature image directly to the PDF
      try {
        pdf.addImage(signatureImageData, 'PNG', finalX, finalY, signatureWidth, signatureHeight)
        console.log('✅ Signature successfully added to PDF')
      } catch (error) {
        console.error('❌ Error adding signature:', error)
        // Fallback: add signature at a fixed position
        pdf.addImage(signatureImageData, 'PNG', 15, pdfHeight - 80, 120, 40)
        console.log('✅ Signature added at fallback position')
      }

      // Save the PDF
      const fileName = `GENESIS_Consent_${participantName.replace(/\s+/g, '_')}_${date}.pdf`
      pdf.save(fileName)

      alert('PDF generated successfully with signature!')

    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Error generating PDF. Please try again.')
    }
  }

  return (
    <div className="consent-container">
      <div ref={formRef} className="consent-form">
        <header className="form-header">
          <h1>GENESIS Program Consent and Acknowledgment</h1>
          <p className="subtitle">
            Thank you for agreeing to enroll in <strong>GENESIS</strong>, a program of{' '}
            <strong>Roseman Medical Group</strong> and{' '}
            <strong>Roseman University College of Medicine</strong>. This form outlines the program details and its limitations.
          </p>
        </header>

        <section className="form-section">
          <h2>Authorization</h2>
          <p>
            I authorize <strong>Roseman Medical Group</strong>, <strong>GENESIS</strong>, and{' '}
            <strong>Roseman University of Health Sciences</strong> to assist me and members of my household 
            in addressing urgent social needs by helping us access social services.
          </p>
        </section>

        <section className="form-section">
          <h2>Understanding of Services</h2>
          <ul>
            <li>I understand that <strong>social needs</strong> include help with food, transportation, health insurance, or housing.</li>
            <li>I understand that <strong>GENESIS</strong> and <strong>Roseman Medical Group</strong> do not directly provide these services, but will help connect me with organizations that can.</li>
            <li>I understand that <strong>GENESIS</strong> operates on behalf of <strong>Roseman Medical Group</strong> when assisting with social needs.</li>
            <li>I understand that assistance may be provided:
              <ul>
                <li>At my home</li>
                <li>At the Roseman Medical Group office</li>
                <li>At the location of a third-party service provider</li>
              </ul>
            </li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Medical Services</h2>
          <ul>
            <li>I understand that <strong>GENESIS</strong> may also help me find <strong>medical services</strong>, which may be provided by Roseman Medical Group or another local healthcare provider.</li>
            <li>I understand that any third-party services will have their own <strong>independent rules and fees</strong>.</li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Home Visits</h2>
          <ul>
            <li>I understand that <strong>home visits</strong> cannot begin until I have successfully completed a <strong>home environment assessment</strong>, which includes disclosing any <strong>past criminal history</strong> by household members.</li>
            <li>I understand that past criminal history <strong>does not automatically exclude</strong> me from participating in GENESIS.</li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Legal Reporting Requirements</h2>
          <ul>
            <li>In accordance with <strong>Nevada Revised Statute (NRS) Chapter 432B</strong>, any person with reasonable cause to believe that child abuse is occurring <strong>must report</strong> to Child Protective Services (CPS) or law enforcement.<br />
            👉 GENESIS staff are <strong>mandated reporters</strong> of suspected child abuse.</li>
            <li>Under <strong>NRS 200.5092</strong>, any professional who suspects abuse or neglect of an <strong>elderly person</strong> (age 60+) or vulnerable adult is <strong>legally required to report it</strong>.<br />
            👉 GENESIS staff are <strong>mandated reporters</strong> of suspected elder abuse.</li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Limitations and Privacy</h2>
          <ul>
            <li>I understand that the success of services depends on <strong>my own participation and effort</strong>.</li>
            <li>I acknowledge that there are <strong>no guarantees</strong> that GENESIS will be successful in securing assistance.</li>
            <li>I understand that <strong>information collected</strong> by GENESIS may be used for <strong>program evaluation</strong>.
              <ul>
                <li>No report or data summary will <strong>specifically identify me or my household</strong>.</li>
              </ul>
            </li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Contact and Complaints</h2>
          <div className="contact-box">
            <p><em>Service excellence is our top priority. If you have a complaint about the service you've received, we take it seriously and are committed to resolving it.</em></p>
            <p>To file a formal complaint, please contact:<br />
            📞 <strong>************</strong></p>
          </div>
        </section>

        <section className="signature-section">
          <div className="participant-info">
            <div className="input-group">
              <label htmlFor="participantName">Participant Name:</label>
              <input
                type="text"
                id="participantName"
                value={participantName}
                onChange={(e) => setParticipantName(e.target.value)}
                placeholder="Enter full name"
                required
              />
            </div>
            <div className="input-group">
              <label htmlFor="date">Date:</label>
              <input
                type="date"
                id="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                required
              />
            </div>
          </div>

          <div className="signature-area">
            <label>Participant Signature:</label>
            <div className="signature-canvas-container">
              <SignatureCanvas
                ref={sigCanvas}
                penColor="black"
                canvasProps={{
                  width: 500,
                  height: 200,
                  className: 'signature-canvas'
                }}
                onEnd={handleSignatureEnd}
              />
            </div>
            <div className="signature-line">
              <span>Signature</span>
            </div>
          </div>
        </section>
      </div>

      <div className="form-controls">
        <button onClick={clearSignature} className="btn btn-secondary">
          Clear Signature
        </button>
        <button onClick={generatePDF} className="btn btn-primary">
          Generate PDF
        </button>
      </div>
    </div>
  )
}

export default ConsentForm
