.consent-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  color: #333;
}

.consent-form {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 3px solid #2c5aa0;
  padding-bottom: 20px;
}

.form-header h1 {
  color: #2c5aa0;
  font-size: 28px;
  margin-bottom: 10px;
  font-weight: bold;
}

.subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.form-section {
  margin-bottom: 25px;
}

.form-section h2 {
  color: #2c5aa0;
  font-size: 20px;
  margin-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 5px;
}

.form-section p {
  margin-bottom: 10px;
}

.form-section ul {
  margin: 10px 0;
  padding-left: 20px;
}

.form-section li {
  margin-bottom: 8px;
}

.form-section ul ul {
  margin-top: 5px;
  margin-bottom: 5px;
}

.contact-box {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  padding: 15px;
  margin: 10px 0;
}

.contact-box p {
  margin: 5px 0;
}

.signature-section {
  margin-top: 40px;
  border-top: 2px solid #2c5aa0;
  padding-top: 30px;
}

.participant-info {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.input-group {
  flex: 1;
  min-width: 200px;
}

.input-group label {
  display: block;
  font-weight: bold;
  margin-bottom: 5px;
  color: #2c5aa0;
}

.input-group input {
  width: 100%;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.input-group input:focus {
  outline: none;
  border-color: #2c5aa0;
}

.signature-area {
  margin-top: 20px;
}

.signature-area label {
  display: block;
  font-weight: bold;
  margin-bottom: 10px;
  color: #2c5aa0;
}

.signature-canvas-container {
  border: 2px solid #ddd;
  border-radius: 4px;
  display: inline-block;
  background: white;
}

.signature-canvas {
  display: block;
  border-radius: 4px;
}

.signature-line {
  margin-top: 10px;
  text-align: center;
  border-top: 1px solid #333;
  padding-top: 5px;
  width: 500px;
  font-size: 14px;
  color: #666;
}

.form-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: #2c5aa0;
  color: white;
}

.btn-primary:hover {
  background: #1e3f73;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(44, 90, 160, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* Print styles for PDF generation */
@media print {
  .form-controls {
    display: none !important;
  }
  
  .consent-form {
    box-shadow: none;
    border: none;
    padding: 20px;
  }
  
  .signature-canvas-container {
    border: 1px solid #000;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .consent-container {
    padding: 10px;
  }
  
  .consent-form {
    padding: 20px;
  }
  
  .participant-info {
    flex-direction: column;
    gap: 15px;
  }
  
  .signature-canvas-container,
  .signature-line {
    width: 100%;
    max-width: 100%;
  }
  
  .signature-canvas {
    width: 100%;
    max-width: 100%;
  }
  
  .form-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 200px;
  }
}
