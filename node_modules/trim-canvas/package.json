{"name": "trim-canvas", "version": "0.1.2", "description": "A tiny library for trimming whitespace from a canvas element", "main": "build/index.js", "devDependencies": {"babel-core": "^6.0.14", "babel-loader": "^6.0.0", "babel-preset-es2015": "^6.6.0", "webpack": "^1.12.2"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "watch": "webpack --watch -d", "produce": "webpack -p", "lint": "standard index.es6"}, "keywords": ["canvas", "trim", "whitespace", "remove", "blanks", "removeBlanks", "remove-blanks"], "repository": {"type": "git", "url": "https://github.com/agilgur5/trim-canvas.git"}, "author": "<PERSON>", "license": "Apache-2.0"}