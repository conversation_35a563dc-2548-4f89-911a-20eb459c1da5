{"version": 3, "sources": ["../../react-is/cjs/react-is.development.js", "../../react-is/index.js", "../../object-assign/index.js", "../../prop-types/lib/ReactPropTypesSecret.js", "../../prop-types/lib/has.js", "../../prop-types/checkPropTypes.js", "../../prop-types/factoryWithTypeCheckers.js", "../../prop-types/index.js", "../../trim-canvas/build/index.js", "../../@babel/runtime/helpers/esm/extends.js", "../../@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../../@babel/runtime/helpers/esm/objectWithoutProperties.js", "../../@babel/runtime/helpers/esm/createClass.js", "../../@babel/runtime/helpers/esm/classCallCheck.js", "../../@babel/runtime/helpers/esm/setPrototypeOf.js", "../../@babel/runtime/helpers/esm/inherits.js", "../../@babel/runtime/helpers/esm/getPrototypeOf.js", "../../@babel/runtime/helpers/esm/isNativeReflectConstruct.js", "../../@babel/runtime/helpers/esm/assertThisInitialized.js", "../../@babel/runtime/helpers/esm/possibleConstructorReturn.js", "../../@babel/runtime/helpers/esm/createSuper.js", "../../signature_pad/dist/signature_pad.mjs", "../../react-signature-canvas/src/index.tsx"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "!function(e,t){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define([],t):\"object\"==typeof exports?exports.trimCanvas=t():e.trimCanvas=t()}(this,function(){return function(e){function t(n){if(r[n])return r[n].exports;var o=r[n]={exports:{},id:n,loaded:!1};return e[n].call(o.exports,o,o.exports,t),o.loaded=!0,o.exports}var r={};return t.m=e,t.c=r,t.p=\"\",t(0)}([function(e,t){\"use strict\";function r(e){var t=e.getContext(\"2d\"),r=e.width,n=e.height,o=t.getImageData(0,0,r,n).data,f=a(!0,r,n,o),i=a(!1,r,n,o),c=u(!0,r,n,o),d=u(!1,r,n,o),p=d-c+1,l=i-f+1,s=t.getImageData(c,f,p,l);return e.width=p,e.height=l,t.clearRect(0,0,p,l),t.putImageData(s,0,0),e}function n(e,t,r,n){return{red:n[4*(r*t+e)],green:n[4*(r*t+e)+1],blue:n[4*(r*t+e)+2],alpha:n[4*(r*t+e)+3]}}function o(e,t,r,o){return n(e,t,r,o).alpha}function a(e,t,r,n){for(var a=e?1:-1,u=e?0:r-1,f=u;e?f<r:f>-1;f+=a)for(var i=0;i<t;i++)if(o(i,f,t,n))return f;return null}function u(e,t,r,n){for(var a=e?1:-1,u=e?0:t-1,f=u;e?f<t:f>-1;f+=a)for(var i=0;i<r;i++)if(o(f,i,t,n))return f;return null}Object.defineProperty(t,\"__esModule\",{value:!0}),t.default=r}])});", "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };", "import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.js\";\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nexport { _objectWithoutProperties as default };", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nexport { _createClass as default };", "function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nexport { _classCallCheck as default };", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && setPrototypeOf(t, e);\n}\nexport { _inherits as default };", "function _getPrototypeOf(t) {\n  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, _getPrototypeOf(t);\n}\nexport { _getPrototypeOf as default };", "function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };", "function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };", "import _typeof from \"./typeof.js\";\nimport assertThisInitialized from \"./assertThisInitialized.js\";\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nexport { _possibleConstructorReturn as default };", "import getPrototypeOf from \"./getPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport possibleConstructorReturn from \"./possibleConstructorReturn.js\";\nfunction _createSuper(t) {\n  var r = isNativeReflectConstruct();\n  return function () {\n    var e,\n      o = getPrototypeOf(t);\n    if (r) {\n      var s = getPrototypeOf(this).constructor;\n      e = Reflect.construct(o, arguments, s);\n    } else e = o.apply(this, arguments);\n    return possibleConstructorReturn(this, e);\n  };\n}\nexport { _createSuper as default };", "/*!\n * Signature Pad v2.3.2\n * https://github.com/szimek/signature_pad\n *\n * Copyright 2017 <PERSON><PERSON><PERSON>\n * Released under the MIT license\n *\n * The main idea and some parts of the code (e.g. drawing variable width B<PERSON><PERSON> curve) are taken from:\n * http://corner.squareup.com/2012/07/smoother-signatures.html\n *\n * Implementation of interpolation using cubic Bézier curves is taken from:\n * http://benknowscode.wordpress.com/2012/09/14/path-interpolation-using-cubic-bezier-and-control-point-estimation-in-javascript\n *\n * Algorithm for approximated length of a Bézier curve is taken from:\n * http://www.lemoda.net/maths/bezier-length/index.html\n *\n */\n\nfunction Point(x, y, time) {\n  this.x = x;\n  this.y = y;\n  this.time = time || new Date().getTime();\n}\n\nPoint.prototype.velocityFrom = function (start) {\n  return this.time !== start.time ? this.distanceTo(start) / (this.time - start.time) : 1;\n};\n\nPoint.prototype.distanceTo = function (start) {\n  return Math.sqrt(Math.pow(this.x - start.x, 2) + Math.pow(this.y - start.y, 2));\n};\n\nPoint.prototype.equals = function (other) {\n  return this.x === other.x && this.y === other.y && this.time === other.time;\n};\n\nfunction Bezier(startPoint, control1, control2, endPoint) {\n  this.startPoint = startPoint;\n  this.control1 = control1;\n  this.control2 = control2;\n  this.endPoint = endPoint;\n}\n\n// Returns approximated length.\nBezier.prototype.length = function () {\n  var steps = 10;\n  var length = 0;\n  var px = void 0;\n  var py = void 0;\n\n  for (var i = 0; i <= steps; i += 1) {\n    var t = i / steps;\n    var cx = this._point(t, this.startPoint.x, this.control1.x, this.control2.x, this.endPoint.x);\n    var cy = this._point(t, this.startPoint.y, this.control1.y, this.control2.y, this.endPoint.y);\n    if (i > 0) {\n      var xdiff = cx - px;\n      var ydiff = cy - py;\n      length += Math.sqrt(xdiff * xdiff + ydiff * ydiff);\n    }\n    px = cx;\n    py = cy;\n  }\n\n  return length;\n};\n\n/* eslint-disable no-multi-spaces, space-in-parens */\nBezier.prototype._point = function (t, start, c1, c2, end) {\n  return start * (1.0 - t) * (1.0 - t) * (1.0 - t) + 3.0 * c1 * (1.0 - t) * (1.0 - t) * t + 3.0 * c2 * (1.0 - t) * t * t + end * t * t * t;\n};\n\n/* eslint-disable */\n\n// http://stackoverflow.com/a/27078401/815507\nfunction throttle(func, wait, options) {\n  var context, args, result;\n  var timeout = null;\n  var previous = 0;\n  if (!options) options = {};\n  var later = function later() {\n    previous = options.leading === false ? 0 : Date.now();\n    timeout = null;\n    result = func.apply(context, args);\n    if (!timeout) context = args = null;\n  };\n  return function () {\n    var now = Date.now();\n    if (!previous && options.leading === false) previous = now;\n    var remaining = wait - (now - previous);\n    context = this;\n    args = arguments;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = now;\n      result = func.apply(context, args);\n      if (!timeout) context = args = null;\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(later, remaining);\n    }\n    return result;\n  };\n}\n\nfunction SignaturePad(canvas, options) {\n  var self = this;\n  var opts = options || {};\n\n  this.velocityFilterWeight = opts.velocityFilterWeight || 0.7;\n  this.minWidth = opts.minWidth || 0.5;\n  this.maxWidth = opts.maxWidth || 2.5;\n  this.throttle = 'throttle' in opts ? opts.throttle : 16; // in miliseconds\n  this.minDistance = 'minDistance' in opts ? opts.minDistance : 5;\n\n  if (this.throttle) {\n    this._strokeMoveUpdate = throttle(SignaturePad.prototype._strokeUpdate, this.throttle);\n  } else {\n    this._strokeMoveUpdate = SignaturePad.prototype._strokeUpdate;\n  }\n\n  this.dotSize = opts.dotSize || function () {\n    return (this.minWidth + this.maxWidth) / 2;\n  };\n  this.penColor = opts.penColor || 'black';\n  this.backgroundColor = opts.backgroundColor || 'rgba(0,0,0,0)';\n  this.onBegin = opts.onBegin;\n  this.onEnd = opts.onEnd;\n\n  this._canvas = canvas;\n  this._ctx = canvas.getContext('2d');\n  this.clear();\n\n  // We need add these inline so they are available to unbind while still having\n  // access to 'self' we could use _.bind but it's not worth adding a dependency.\n  this._handleMouseDown = function (event) {\n    if (event.which === 1) {\n      self._mouseButtonDown = true;\n      self._strokeBegin(event);\n    }\n  };\n\n  this._handleMouseMove = function (event) {\n    if (self._mouseButtonDown) {\n      self._strokeMoveUpdate(event);\n    }\n  };\n\n  this._handleMouseUp = function (event) {\n    if (event.which === 1 && self._mouseButtonDown) {\n      self._mouseButtonDown = false;\n      self._strokeEnd(event);\n    }\n  };\n\n  this._handleTouchStart = function (event) {\n    if (event.targetTouches.length === 1) {\n      var touch = event.changedTouches[0];\n      self._strokeBegin(touch);\n    }\n  };\n\n  this._handleTouchMove = function (event) {\n    // Prevent scrolling.\n    event.preventDefault();\n\n    var touch = event.targetTouches[0];\n    self._strokeMoveUpdate(touch);\n  };\n\n  this._handleTouchEnd = function (event) {\n    var wasCanvasTouched = event.target === self._canvas;\n    if (wasCanvasTouched) {\n      event.preventDefault();\n      self._strokeEnd(event);\n    }\n  };\n\n  // Enable mouse and touch event handlers\n  this.on();\n}\n\n// Public methods\nSignaturePad.prototype.clear = function () {\n  var ctx = this._ctx;\n  var canvas = this._canvas;\n\n  ctx.fillStyle = this.backgroundColor;\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n  this._data = [];\n  this._reset();\n  this._isEmpty = true;\n};\n\nSignaturePad.prototype.fromDataURL = function (dataUrl) {\n  var _this = this;\n\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  var image = new Image();\n  var ratio = options.ratio || window.devicePixelRatio || 1;\n  var width = options.width || this._canvas.width / ratio;\n  var height = options.height || this._canvas.height / ratio;\n\n  this._reset();\n  image.src = dataUrl;\n  image.onload = function () {\n    _this._ctx.drawImage(image, 0, 0, width, height);\n  };\n  this._isEmpty = false;\n};\n\nSignaturePad.prototype.toDataURL = function (type) {\n  var _canvas;\n\n  switch (type) {\n    case 'image/svg+xml':\n      return this._toSVG();\n    default:\n      for (var _len = arguments.length, options = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        options[_key - 1] = arguments[_key];\n      }\n\n      return (_canvas = this._canvas).toDataURL.apply(_canvas, [type].concat(options));\n  }\n};\n\nSignaturePad.prototype.on = function () {\n  this._handleMouseEvents();\n  this._handleTouchEvents();\n};\n\nSignaturePad.prototype.off = function () {\n  this._canvas.removeEventListener('mousedown', this._handleMouseDown);\n  this._canvas.removeEventListener('mousemove', this._handleMouseMove);\n  document.removeEventListener('mouseup', this._handleMouseUp);\n\n  this._canvas.removeEventListener('touchstart', this._handleTouchStart);\n  this._canvas.removeEventListener('touchmove', this._handleTouchMove);\n  this._canvas.removeEventListener('touchend', this._handleTouchEnd);\n};\n\nSignaturePad.prototype.isEmpty = function () {\n  return this._isEmpty;\n};\n\n// Private methods\nSignaturePad.prototype._strokeBegin = function (event) {\n  this._data.push([]);\n  this._reset();\n  this._strokeUpdate(event);\n\n  if (typeof this.onBegin === 'function') {\n    this.onBegin(event);\n  }\n};\n\nSignaturePad.prototype._strokeUpdate = function (event) {\n  var x = event.clientX;\n  var y = event.clientY;\n\n  var point = this._createPoint(x, y);\n  var lastPointGroup = this._data[this._data.length - 1];\n  var lastPoint = lastPointGroup && lastPointGroup[lastPointGroup.length - 1];\n  var isLastPointTooClose = lastPoint && point.distanceTo(lastPoint) < this.minDistance;\n\n  // Skip this point if it's too close to the previous one\n  if (!(lastPoint && isLastPointTooClose)) {\n    var _addPoint = this._addPoint(point),\n        curve = _addPoint.curve,\n        widths = _addPoint.widths;\n\n    if (curve && widths) {\n      this._drawCurve(curve, widths.start, widths.end);\n    }\n\n    this._data[this._data.length - 1].push({\n      x: point.x,\n      y: point.y,\n      time: point.time,\n      color: this.penColor\n    });\n  }\n};\n\nSignaturePad.prototype._strokeEnd = function (event) {\n  var canDrawCurve = this.points.length > 2;\n  var point = this.points[0]; // Point instance\n\n  if (!canDrawCurve && point) {\n    this._drawDot(point);\n  }\n\n  if (point) {\n    var lastPointGroup = this._data[this._data.length - 1];\n    var lastPoint = lastPointGroup[lastPointGroup.length - 1]; // plain object\n\n    // When drawing a dot, there's only one point in a group, so without this check\n    // such group would end up with exactly the same 2 points.\n    if (!point.equals(lastPoint)) {\n      lastPointGroup.push({\n        x: point.x,\n        y: point.y,\n        time: point.time,\n        color: this.penColor\n      });\n    }\n  }\n\n  if (typeof this.onEnd === 'function') {\n    this.onEnd(event);\n  }\n};\n\nSignaturePad.prototype._handleMouseEvents = function () {\n  this._mouseButtonDown = false;\n\n  this._canvas.addEventListener('mousedown', this._handleMouseDown);\n  this._canvas.addEventListener('mousemove', this._handleMouseMove);\n  document.addEventListener('mouseup', this._handleMouseUp);\n};\n\nSignaturePad.prototype._handleTouchEvents = function () {\n  // Pass touch events to canvas element on mobile IE11 and Edge.\n  this._canvas.style.msTouchAction = 'none';\n  this._canvas.style.touchAction = 'none';\n\n  this._canvas.addEventListener('touchstart', this._handleTouchStart);\n  this._canvas.addEventListener('touchmove', this._handleTouchMove);\n  this._canvas.addEventListener('touchend', this._handleTouchEnd);\n};\n\nSignaturePad.prototype._reset = function () {\n  this.points = [];\n  this._lastVelocity = 0;\n  this._lastWidth = (this.minWidth + this.maxWidth) / 2;\n  this._ctx.fillStyle = this.penColor;\n};\n\nSignaturePad.prototype._createPoint = function (x, y, time) {\n  var rect = this._canvas.getBoundingClientRect();\n\n  return new Point(x - rect.left, y - rect.top, time || new Date().getTime());\n};\n\nSignaturePad.prototype._addPoint = function (point) {\n  var points = this.points;\n  var tmp = void 0;\n\n  points.push(point);\n\n  if (points.length > 2) {\n    // To reduce the initial lag make it work with 3 points\n    // by copying the first point to the beginning.\n    if (points.length === 3) points.unshift(points[0]);\n\n    tmp = this._calculateCurveControlPoints(points[0], points[1], points[2]);\n    var c2 = tmp.c2;\n    tmp = this._calculateCurveControlPoints(points[1], points[2], points[3]);\n    var c3 = tmp.c1;\n    var curve = new Bezier(points[1], c2, c3, points[2]);\n    var widths = this._calculateCurveWidths(curve);\n\n    // Remove the first element from the list,\n    // so that we always have no more than 4 points in points array.\n    points.shift();\n\n    return { curve: curve, widths: widths };\n  }\n\n  return {};\n};\n\nSignaturePad.prototype._calculateCurveControlPoints = function (s1, s2, s3) {\n  var dx1 = s1.x - s2.x;\n  var dy1 = s1.y - s2.y;\n  var dx2 = s2.x - s3.x;\n  var dy2 = s2.y - s3.y;\n\n  var m1 = { x: (s1.x + s2.x) / 2.0, y: (s1.y + s2.y) / 2.0 };\n  var m2 = { x: (s2.x + s3.x) / 2.0, y: (s2.y + s3.y) / 2.0 };\n\n  var l1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n  var l2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);\n\n  var dxm = m1.x - m2.x;\n  var dym = m1.y - m2.y;\n\n  var k = l2 / (l1 + l2);\n  var cm = { x: m2.x + dxm * k, y: m2.y + dym * k };\n\n  var tx = s2.x - cm.x;\n  var ty = s2.y - cm.y;\n\n  return {\n    c1: new Point(m1.x + tx, m1.y + ty),\n    c2: new Point(m2.x + tx, m2.y + ty)\n  };\n};\n\nSignaturePad.prototype._calculateCurveWidths = function (curve) {\n  var startPoint = curve.startPoint;\n  var endPoint = curve.endPoint;\n  var widths = { start: null, end: null };\n\n  var velocity = this.velocityFilterWeight * endPoint.velocityFrom(startPoint) + (1 - this.velocityFilterWeight) * this._lastVelocity;\n\n  var newWidth = this._strokeWidth(velocity);\n\n  widths.start = this._lastWidth;\n  widths.end = newWidth;\n\n  this._lastVelocity = velocity;\n  this._lastWidth = newWidth;\n\n  return widths;\n};\n\nSignaturePad.prototype._strokeWidth = function (velocity) {\n  return Math.max(this.maxWidth / (velocity + 1), this.minWidth);\n};\n\nSignaturePad.prototype._drawPoint = function (x, y, size) {\n  var ctx = this._ctx;\n\n  ctx.moveTo(x, y);\n  ctx.arc(x, y, size, 0, 2 * Math.PI, false);\n  this._isEmpty = false;\n};\n\nSignaturePad.prototype._drawCurve = function (curve, startWidth, endWidth) {\n  var ctx = this._ctx;\n  var widthDelta = endWidth - startWidth;\n  var drawSteps = Math.floor(curve.length());\n\n  ctx.beginPath();\n\n  for (var i = 0; i < drawSteps; i += 1) {\n    // Calculate the Bezier (x, y) coordinate for this step.\n    var t = i / drawSteps;\n    var tt = t * t;\n    var ttt = tt * t;\n    var u = 1 - t;\n    var uu = u * u;\n    var uuu = uu * u;\n\n    var x = uuu * curve.startPoint.x;\n    x += 3 * uu * t * curve.control1.x;\n    x += 3 * u * tt * curve.control2.x;\n    x += ttt * curve.endPoint.x;\n\n    var y = uuu * curve.startPoint.y;\n    y += 3 * uu * t * curve.control1.y;\n    y += 3 * u * tt * curve.control2.y;\n    y += ttt * curve.endPoint.y;\n\n    var width = startWidth + ttt * widthDelta;\n    this._drawPoint(x, y, width);\n  }\n\n  ctx.closePath();\n  ctx.fill();\n};\n\nSignaturePad.prototype._drawDot = function (point) {\n  var ctx = this._ctx;\n  var width = typeof this.dotSize === 'function' ? this.dotSize() : this.dotSize;\n\n  ctx.beginPath();\n  this._drawPoint(point.x, point.y, width);\n  ctx.closePath();\n  ctx.fill();\n};\n\nSignaturePad.prototype._fromData = function (pointGroups, drawCurve, drawDot) {\n  for (var i = 0; i < pointGroups.length; i += 1) {\n    var group = pointGroups[i];\n\n    if (group.length > 1) {\n      for (var j = 0; j < group.length; j += 1) {\n        var rawPoint = group[j];\n        var point = new Point(rawPoint.x, rawPoint.y, rawPoint.time);\n        var color = rawPoint.color;\n\n        if (j === 0) {\n          // First point in a group. Nothing to draw yet.\n\n          // All points in the group have the same color, so it's enough to set\n          // penColor just at the beginning.\n          this.penColor = color;\n          this._reset();\n\n          this._addPoint(point);\n        } else if (j !== group.length - 1) {\n          // Middle point in a group.\n          var _addPoint2 = this._addPoint(point),\n              curve = _addPoint2.curve,\n              widths = _addPoint2.widths;\n\n          if (curve && widths) {\n            drawCurve(curve, widths, color);\n          }\n        } else {\n          // Last point in a group. Do nothing.\n        }\n      }\n    } else {\n      this._reset();\n      var _rawPoint = group[0];\n      drawDot(_rawPoint);\n    }\n  }\n};\n\nSignaturePad.prototype._toSVG = function () {\n  var _this2 = this;\n\n  var pointGroups = this._data;\n  var canvas = this._canvas;\n  var ratio = Math.max(window.devicePixelRatio || 1, 1);\n  var minX = 0;\n  var minY = 0;\n  var maxX = canvas.width / ratio;\n  var maxY = canvas.height / ratio;\n  var svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n\n  svg.setAttributeNS(null, 'width', canvas.width);\n  svg.setAttributeNS(null, 'height', canvas.height);\n\n  this._fromData(pointGroups, function (curve, widths, color) {\n    var path = document.createElement('path');\n\n    // Need to check curve for NaN values, these pop up when drawing\n    // lines on the canvas that are not continuous. E.g. Sharp corners\n    // or stopping mid-stroke and than continuing without lifting mouse.\n    if (!isNaN(curve.control1.x) && !isNaN(curve.control1.y) && !isNaN(curve.control2.x) && !isNaN(curve.control2.y)) {\n      var attr = 'M ' + curve.startPoint.x.toFixed(3) + ',' + curve.startPoint.y.toFixed(3) + ' ' + ('C ' + curve.control1.x.toFixed(3) + ',' + curve.control1.y.toFixed(3) + ' ') + (curve.control2.x.toFixed(3) + ',' + curve.control2.y.toFixed(3) + ' ') + (curve.endPoint.x.toFixed(3) + ',' + curve.endPoint.y.toFixed(3));\n\n      path.setAttribute('d', attr);\n      path.setAttribute('stroke-width', (widths.end * 2.25).toFixed(3));\n      path.setAttribute('stroke', color);\n      path.setAttribute('fill', 'none');\n      path.setAttribute('stroke-linecap', 'round');\n\n      svg.appendChild(path);\n    }\n  }, function (rawPoint) {\n    var circle = document.createElement('circle');\n    var dotSize = typeof _this2.dotSize === 'function' ? _this2.dotSize() : _this2.dotSize;\n    circle.setAttribute('r', dotSize);\n    circle.setAttribute('cx', rawPoint.x);\n    circle.setAttribute('cy', rawPoint.y);\n    circle.setAttribute('fill', rawPoint.color);\n\n    svg.appendChild(circle);\n  });\n\n  var prefix = 'data:image/svg+xml;base64,';\n  var header = '<svg' + ' xmlns=\"http://www.w3.org/2000/svg\"' + ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"' + (' viewBox=\"' + minX + ' ' + minY + ' ' + maxX + ' ' + maxY + '\"') + (' width=\"' + maxX + '\"') + (' height=\"' + maxY + '\"') + '>';\n  var body = svg.innerHTML;\n\n  // IE hack for missing innerHTML property on SVGElement\n  if (body === undefined) {\n    var dummy = document.createElement('dummy');\n    var nodes = svg.childNodes;\n    dummy.innerHTML = '';\n\n    for (var i = 0; i < nodes.length; i += 1) {\n      dummy.appendChild(nodes[i].cloneNode(true));\n    }\n\n    body = dummy.innerHTML;\n  }\n\n  var footer = '</svg>';\n  var data = header + body + footer;\n\n  return prefix + btoa(data);\n};\n\nSignaturePad.prototype.fromData = function (pointGroups) {\n  var _this3 = this;\n\n  this.clear();\n\n  this._fromData(pointGroups, function (curve, widths) {\n    return _this3._drawCurve(curve, widths.start, widths.end);\n  }, function (rawPoint) {\n    return _this3._drawDot(rawPoint);\n  });\n\n  this._data = pointGroups;\n};\n\nSignaturePad.prototype.toData = function () {\n  return this._data;\n};\n\nexport default SignaturePad;\n", "import PropTypes from 'prop-types'\nimport React, { Component } from 'react'\nimport SignaturePad from 'signature_pad'\nimport trimCanvas from 'trim-canvas'\n\nexport interface SignatureCanvasProps extends SignaturePad.SignaturePadOptions {\n  canvasProps?: React.CanvasHTMLAttributes<HTMLCanvasElement>\n  clearOnResize?: boolean\n}\n\nexport class SignatureCanvas extends Component<SignatureCanvasProps> {\n  static override propTypes = {\n    // signature_pad's props\n    velocityFilterWeight: PropTypes.number,\n    minWidth: PropTypes.number,\n    maxWidth: PropTypes.number,\n    minDistance: PropTypes.number,\n    dotSize: PropTypes.oneOfType([PropTypes.number, PropTypes.func]),\n    penColor: PropTypes.string,\n    throttle: PropTypes.number,\n    onEnd: PropTypes.func,\n    onBegin: PropTypes.func,\n    // props specific to the React wrapper\n    canvasProps: PropTypes.object,\n    clearOnResize: PropTypes.bool\n  }\n\n  static defaultProps: Pick<SignatureCanvasProps, 'clearOnResize'> = {\n    clearOnResize: true\n  }\n\n  static refNullError = new Error('react-signature-canvas is currently ' +\n    'mounting or unmounting: React refs are null during this phase.')\n\n  // shortcut reference (https://stackoverflow.com/a/29244254/3431180)\n  private readonly staticThis = this.constructor as typeof SignatureCanvas\n\n  _sigPad: SignaturePad | null = null\n  _canvas: HTMLCanvasElement | null = null\n\n  private readonly setRef = (ref: HTMLCanvasElement | null): void => {\n    this._canvas = ref\n    // if component is unmounted, set internal references to null\n    if (this._canvas === null) {\n      this._sigPad = null\n    }\n  }\n\n  _excludeOurProps = (): SignaturePad.SignaturePadOptions => {\n    const { canvasProps, clearOnResize, ...sigPadProps } = this.props\n    return sigPadProps\n  }\n\n  override componentDidMount: Component['componentDidMount'] = () => {\n    const canvas = this.getCanvas()\n    this._sigPad = new SignaturePad(canvas, this._excludeOurProps())\n    this._resizeCanvas()\n    this.on()\n  }\n\n  override componentWillUnmount: Component['componentWillUnmount'] = () => {\n    this.off()\n  }\n\n  // propagate prop updates to SignaturePad\n  override componentDidUpdate: Component['componentDidUpdate'] = () => {\n    Object.assign(this._sigPad, this._excludeOurProps())\n  }\n\n  // return the canvas ref for operations like toDataURL\n  getCanvas = (): HTMLCanvasElement => {\n    if (this._canvas === null) {\n      throw this.staticThis.refNullError\n    }\n    return this._canvas\n  }\n\n  // return a trimmed copy of the canvas\n  getTrimmedCanvas = (): HTMLCanvasElement => {\n    // copy the canvas\n    const canvas = this.getCanvas()\n    const copy = document.createElement('canvas')\n    copy.width = canvas.width\n    copy.height = canvas.height\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    copy.getContext('2d')!.drawImage(canvas, 0, 0)\n    // then trim it\n    return trimCanvas(copy)\n  }\n\n  // return the internal SignaturePad reference\n  getSignaturePad = (): SignaturePad => {\n    if (this._sigPad === null) {\n      throw this.staticThis.refNullError\n    }\n    return this._sigPad\n  }\n\n  _checkClearOnResize = (): void => {\n    if (!this.props.clearOnResize) { // eslint-disable-line @typescript-eslint/strict-boolean-expressions -- this is backward compatible with the previous behavior, where null was treated as falsey\n      return\n    }\n    this._resizeCanvas()\n  }\n\n  _resizeCanvas = (): void => {\n    const canvasProps = this.props.canvasProps ?? {}\n    const { width, height } = canvasProps\n    // don't resize if the canvas has fixed width and height\n    if (typeof width !== 'undefined' && typeof height !== 'undefined') {\n      return\n    }\n\n    const canvas = this.getCanvas()\n    /* When zoomed out to less than 100%, for some very strange reason,\n      some browsers report devicePixelRatio as less than 1\n      and only part of the canvas is cleared then. */\n    const ratio = Math.max(window.devicePixelRatio ?? 1, 1)\n\n    if (typeof width === 'undefined') {\n      canvas.width = canvas.offsetWidth * ratio\n    }\n    if (typeof height === 'undefined') {\n      canvas.height = canvas.offsetHeight * ratio\n    }\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    canvas.getContext('2d')!.scale(ratio, ratio)\n    this.clear()\n  }\n\n  override render: Component['render'] = () => {\n    const { canvasProps } = this.props\n    return <canvas ref={this.setRef} {...canvasProps} />\n  }\n\n  // all wrapper functions below render\n  //\n  on: SignaturePad['on'] = () => {\n    window.addEventListener('resize', this._checkClearOnResize)\n    return this.getSignaturePad().on()\n  }\n\n  off: SignaturePad['off'] = () => {\n    window.removeEventListener('resize', this._checkClearOnResize)\n    return this.getSignaturePad().off()\n  }\n\n  clear: SignaturePad['clear'] = () => {\n    return this.getSignaturePad().clear()\n  }\n\n  isEmpty: SignaturePad['isEmpty'] = () => {\n    return this.getSignaturePad().isEmpty()\n  }\n\n  fromDataURL: SignaturePad['fromDataURL'] = (dataURL, options) => {\n    return this.getSignaturePad().fromDataURL(dataURL, options)\n  }\n\n  toDataURL: SignaturePad['toDataURL'] = (type, encoderOptions) => {\n    return this.getSignaturePad().toDataURL(type, encoderOptions)\n  }\n\n  fromData: SignaturePad['fromData'] = (pointGroups) => {\n    return this.getSignaturePad().fromData(pointGroups)\n  }\n\n  toData: SignaturePad['toData'] = () => {\n    return this.getSignaturePad().toData()\n  }\n}\n\nexport default SignatureCanvas\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAaA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAIA,YAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AACvD,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AACnE,YAAI,oBAAoB,YAAY,OAAO,IAAI,cAAc,IAAI;AACjE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AAGnE,YAAI,wBAAwB,YAAY,OAAO,IAAI,kBAAkB,IAAI;AACzE,YAAI,6BAA6B,YAAY,OAAO,IAAI,uBAAuB,IAAI;AACnF,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,2BAA2B,YAAY,OAAO,IAAI,qBAAqB,IAAI;AAC/E,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAC/D,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,uBAAuB,YAAY,OAAO,IAAI,iBAAiB,IAAI;AACvE,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAE/D,iBAAS,mBAAmB,MAAM;AAChC,iBAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA,UACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;AAAA,QACplB;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAE1C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,+KAAyL;AAAA,YAC3M;AAAA,UACF;AAEA,iBAAO,iBAAiB,MAAM,KAAK,OAAO,MAAM,MAAM;AAAA,QACxD;AACA,iBAAS,iBAAiB,QAAQ;AAChC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,YAAY;AACpB,gBAAQ,iBAAiB;AACzB,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAU;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoB;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAY;AACpB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqB;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpLA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAQA,QAAI,wBAAwB,OAAO;AACnC,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,mBAAmB,OAAO,UAAU;AAExC,aAAS,SAAS,KAAK;AACtB,UAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,cAAM,IAAI,UAAU,uDAAuD;AAAA,MAC5E;AAEA,aAAO,OAAO,GAAG;AAAA,IAClB;AAEA,aAAS,kBAAkB;AAC1B,UAAI;AACH,YAAI,CAAC,OAAO,QAAQ;AACnB,iBAAO;AAAA,QACR;AAKA,YAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,cAAM,CAAC,IAAI;AACX,YAAI,OAAO,oBAAoB,KAAK,EAAE,CAAC,MAAM,KAAK;AACjD,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,gBAAM,MAAM,OAAO,aAAa,CAAC,CAAC,IAAI;AAAA,QACvC;AACA,YAAI,SAAS,OAAO,oBAAoB,KAAK,EAAE,IAAI,SAAU,GAAG;AAC/D,iBAAO,MAAM,CAAC;AAAA,QACf,CAAC;AACD,YAAI,OAAO,KAAK,EAAE,MAAM,cAAc;AACrC,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,+BAAuB,MAAM,EAAE,EAAE,QAAQ,SAAU,QAAQ;AAC1D,gBAAM,MAAM,IAAI;AAAA,QACjB,CAAC;AACD,YAAI,OAAO,KAAK,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAC/C,wBAAwB;AACzB,iBAAO;AAAA,QACR;AAEA,eAAO;AAAA,MACR,SAAS,KAAK;AAEb,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU,gBAAgB,IAAI,OAAO,SAAS,SAAU,QAAQ,QAAQ;AAC9E,UAAI;AACJ,UAAI,KAAK,SAAS,MAAM;AACxB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,eAAO,OAAO,UAAU,CAAC,CAAC;AAE1B,iBAAS,OAAO,MAAM;AACrB,cAAI,eAAe,KAAK,MAAM,GAAG,GAAG;AACnC,eAAG,GAAG,IAAI,KAAK,GAAG;AAAA,UACnB;AAAA,QACD;AAEA,YAAI,uBAAuB;AAC1B,oBAAU,sBAAsB,IAAI;AACpC,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,gBAAI,iBAAiB,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AAC5C,iBAAG,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzFA;AAAA;AAAA;AASA,QAAI,uBAAuB;AAE3B,WAAO,UAAU;AAAA;AAAA;;;ACXjB;AAAA;AAAA,WAAO,UAAU,SAAS,KAAK,KAAK,OAAO,UAAU,cAAc;AAAA;AAAA;;;ACAnE;AAAA;AAAA;AASA,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACrC,6BAAuB;AACvB,2BAAqB,CAAC;AACtB,YAAM;AAEV,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAO;AAAA,MACrB;AAAA,IACF;AAhBM;AACA;AACA;AA2BN,aAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,UAAU;AAC5E,UAAI,MAAuC;AACzC,iBAAS,gBAAgB,WAAW;AAClC,cAAI,IAAI,WAAW,YAAY,GAAG;AAChC,gBAAI;AAIJ,gBAAI;AAGF,kBAAI,OAAO,UAAU,YAAY,MAAM,YAAY;AACjD,oBAAI,MAAM;AAAA,mBACP,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FACC,OAAO,UAAU,YAAY,IAAI;AAAA,gBAEpH;AACA,oBAAI,OAAO;AACX,sBAAM;AAAA,cACR;AACA,sBAAQ,UAAU,YAAY,EAAE,QAAQ,cAAc,eAAe,UAAU,MAAM,oBAAoB;AAAA,YAC3G,SAAS,IAAI;AACX,sBAAQ;AAAA,YACV;AACA,gBAAI,SAAS,EAAE,iBAAiB,QAAQ;AACtC;AAAA,iBACG,iBAAiB,iBAAiB,6BACnC,WAAW,OAAO,eAAe,6FAC6B,OAAO,QAAQ;AAAA,cAI/E;AAAA,YACF;AACA,gBAAI,iBAAiB,SAAS,EAAE,MAAM,WAAW,qBAAqB;AAGpE,iCAAmB,MAAM,OAAO,IAAI;AAEpC,kBAAI,QAAQ,WAAW,SAAS,IAAI;AAEpC;AAAA,gBACE,YAAY,WAAW,YAAY,MAAM,WAAW,SAAS,OAAO,QAAQ;AAAA,cAC9E;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,mBAAe,oBAAoB,WAAW;AAC5C,UAAI,MAAuC;AACzC,6BAAqB,CAAC;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtGjB;AAAA;AAAA;AASA,QAAI,UAAU;AACd,QAAI,SAAS;AAEb,QAAI,uBAAuB;AAC3B,QAAI,MAAM;AACV,QAAI,iBAAiB;AAErB,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACzC,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAAA,IACF;AAEA,aAAS,+BAA+B;AACtC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,SAAS,gBAAgB,qBAAqB;AAE7D,UAAI,kBAAkB,OAAO,WAAW,cAAc,OAAO;AAC7D,UAAI,uBAAuB;AAgB3B,eAAS,cAAc,eAAe;AACpC,YAAI,aAAa,kBAAkB,mBAAmB,cAAc,eAAe,KAAK,cAAc,oBAAoB;AAC1H,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT;AAAA,MACF;AAiDA,UAAI,YAAY;AAIhB,UAAI,iBAAiB;AAAA,QACnB,OAAO,2BAA2B,OAAO;AAAA,QACzC,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,MAAM,2BAA2B,SAAS;AAAA,QAC1C,MAAM,2BAA2B,UAAU;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAE3C,KAAK,qBAAqB;AAAA,QAC1B,SAAS;AAAA,QACT,SAAS,yBAAyB;AAAA,QAClC,aAAa,6BAA6B;AAAA,QAC1C,YAAY;AAAA,QACZ,MAAM,kBAAkB;AAAA,QACxB,UAAU;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAOA,eAAS,GAAG,GAAG,GAAG;AAEhB,YAAI,MAAM,GAAG;AAGX,iBAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,QAClC,OAAO;AAEL,iBAAO,MAAM,KAAK,MAAM;AAAA,QAC1B;AAAA,MACF;AAUA,eAAS,cAAc,SAAS,MAAM;AACpC,aAAK,UAAU;AACf,aAAK,OAAO,QAAQ,OAAO,SAAS,WAAW,OAAM,CAAC;AACtD,aAAK,QAAQ;AAAA,MACf;AAEA,oBAAc,YAAY,MAAM;AAEhC,eAAS,2BAA2B,UAAU;AAC5C,YAAI,MAAuC;AACzC,cAAI,0BAA0B,CAAC;AAC/B,cAAI,6BAA6B;AAAA,QACnC;AACA,iBAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAC7F,0BAAgB,iBAAiB;AACjC,yBAAe,gBAAgB;AAE/B,cAAI,WAAW,sBAAsB;AACnC,gBAAI,qBAAqB;AAEvB,kBAAI,MAAM,IAAI;AAAA,gBACZ;AAAA,cAGF;AACA,kBAAI,OAAO;AACX,oBAAM;AAAA,YACR,WAAoD,OAAO,YAAY,aAAa;AAElF,kBAAI,WAAW,gBAAgB,MAAM;AACrC,kBACE,CAAC,wBAAwB,QAAQ;AAAA,cAEjC,6BAA6B,GAC7B;AACA;AAAA,kBACE,6EACuB,eAAe,gBAAgB,gBAAgB;AAAA,gBAIxE;AACA,wCAAwB,QAAQ,IAAI;AACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,gBAAI,YAAY;AACd,kBAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,uBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,8BAA8B,SAAS,gBAAgB,8BAA8B;AAAA,cAC1J;AACA,qBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,iCAAiC,MAAM,gBAAgB,mCAAmC;AAAA,YAC/J;AACA,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,OAAO,UAAU,eAAe,UAAU,YAAY;AAAA,UACxE;AAAA,QACF;AAEA,YAAI,mBAAmB,UAAU,KAAK,MAAM,KAAK;AACjD,yBAAiB,aAAa,UAAU,KAAK,MAAM,IAAI;AAEvD,eAAO;AAAA,MACT;AAEA,eAAS,2BAA2B,cAAc;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAChF,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,cAAc;AAI7B,gBAAI,cAAc,eAAe,SAAS;AAE1C,mBAAO,IAAI;AAAA,cACT,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,cAAc,oBAAoB,gBAAgB,mBAAmB,MAAM,eAAe;AAAA,cAC9J,EAAC,aAA0B;AAAA,YAC7B;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB;AAC9B,eAAO,2BAA2B,4BAA4B;AAAA,MAChE;AAEA,eAAS,yBAAyB,aAAa;AAC7C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,iDAAiD;AAAA,UAC/I;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK,oBAAoB;AACjH,gBAAI,iBAAiB,OAAO;AAC1B,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,2BAA2B;AAClC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,eAAe,SAAS,GAAG;AAC9B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,qCAAqC;AAAA,UACnL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,+BAA+B;AACtC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,QAAQ,mBAAmB,SAAS,GAAG;AAC1C,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,0CAA0C;AAAA,UACxL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,eAAe;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,EAAE,MAAM,QAAQ,aAAa,gBAAgB;AAC/C,gBAAI,oBAAoB,cAAc,QAAQ;AAC9C,gBAAI,kBAAkB,aAAa,MAAM,QAAQ,CAAC;AAClD,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,kBAAkB,oBAAoB,gBAAgB,mBAAmB,kBAAkB,oBAAoB,KAAK;AAAA,UACnN;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,gBAAgB;AAC7C,YAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAClC,cAAI,MAAuC;AACzC,gBAAI,UAAU,SAAS,GAAG;AACxB;AAAA,gBACE,iEAAiE,UAAU,SAAS;AAAA,cAEtF;AAAA,YACF,OAAO;AACL,2BAAa,wDAAwD;AAAA,YACvE;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,mBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,gBAAI,GAAG,WAAW,eAAe,CAAC,CAAC,GAAG;AACpC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI,eAAe,KAAK,UAAU,gBAAgB,SAAS,SAAS,KAAK,OAAO;AAC9E,gBAAI,OAAO,eAAe,KAAK;AAC/B,gBAAI,SAAS,UAAU;AACrB,qBAAO,OAAO,KAAK;AAAA,YACrB;AACA,mBAAO;AAAA,UACT,CAAC;AACD,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,SAAS,IAAI,QAAQ,kBAAkB,gBAAgB,wBAAwB,eAAe,IAAI;AAAA,QACnM;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,aAAa;AAC9C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,kDAAkD;AAAA,UAChJ;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,yBAAyB;AAAA,UACvK;AACA,mBAAS,OAAO,WAAW;AACzB,gBAAI,IAAI,WAAW,GAAG,GAAG;AACvB,kBAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC/G,kBAAI,iBAAiB,OAAO;AAC1B,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB,qBAAqB;AACnD,YAAI,CAAC,MAAM,QAAQ,mBAAmB,GAAG;AACvC,iBAAwC,aAAa,wEAAwE,IAAI;AACjI,iBAAO;AAAA,QACT;AAEA,iBAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,cAAI,UAAU,oBAAoB,CAAC;AACnC,cAAI,OAAO,YAAY,YAAY;AACjC;AAAA,cACE,gGACc,yBAAyB,OAAO,IAAI,eAAe,IAAI;AAAA,YACvE;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,gBAAgB,CAAC;AACrB,mBAASA,KAAI,GAAGA,KAAI,oBAAoB,QAAQA,MAAK;AACnD,gBAAIC,WAAU,oBAAoBD,EAAC;AACnC,gBAAI,gBAAgBC,SAAQ,OAAO,UAAU,eAAe,UAAU,cAAc,oBAAoB;AACxG,gBAAI,iBAAiB,MAAM;AACzB,qBAAO;AAAA,YACT;AACA,gBAAI,cAAc,QAAQ,IAAI,cAAc,MAAM,cAAc,GAAG;AACjE,4BAAc,KAAK,cAAc,KAAK,YAAY;AAAA,YACpD;AAAA,UACF;AACA,cAAI,uBAAwB,cAAc,SAAS,IAAK,6BAA6B,cAAc,KAAK,IAAI,IAAI,MAAK;AACrH,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI;AAAA,QACpJ;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,oBAAoB;AAC3B,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,GAAG;AAC5B,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,2BAA2B;AAAA,UAC9I;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,eAAe,UAAU,cAAc,KAAK,MAAM;AAC/E,eAAO,IAAI;AAAA,WACR,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,+FACX,OAAO;AAAA,QAC1F;AAAA,MACF;AAEA,eAAS,uBAAuB,YAAY;AAC1C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,OAAO,YAAY;AAC1B,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,OAAO,YAAY,YAAY;AACjC,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,6BAA6B,YAAY;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AAEA,cAAI,UAAU,OAAO,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU;AACpD,mBAAS,OAAO,SAAS;AACvB,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,IAAI,YAAY,GAAG,KAAK,OAAO,YAAY,YAAY;AACzD,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,CAAC,SAAS;AACZ,qBAAO,IAAI;AAAA,gBACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,qBACjF,KAAK,UAAU,MAAM,QAAQ,GAAG,MAAM,IAAI,IAC7D,mBAAmB,KAAK,UAAU,OAAO,KAAK,UAAU,GAAG,MAAM,IAAI;AAAA,cACvE;AAAA,YACF;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,OAAO,WAAW;AACzB,gBAAQ,OAAO,WAAW;AAAA,UACxB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO,CAAC;AAAA,UACV,KAAK;AACH,gBAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,qBAAO,UAAU,MAAM,MAAM;AAAA,YAC/B;AACA,gBAAI,cAAc,QAAQ,eAAe,SAAS,GAAG;AACnD,qBAAO;AAAA,YACT;AAEA,gBAAI,aAAa,cAAc,SAAS;AACxC,gBAAI,YAAY;AACd,kBAAI,WAAW,WAAW,KAAK,SAAS;AACxC,kBAAI;AACJ,kBAAI,eAAe,UAAU,SAAS;AACpC,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF,OAAO;AAEL,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,QAAQ,KAAK;AACjB,sBAAI,OAAO;AACT,wBAAI,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG;AACrB,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AAEA,mBAAO;AAAA,UACT;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAEA,eAAS,SAAS,UAAU,WAAW;AAErC,YAAI,aAAa,UAAU;AACzB,iBAAO;AAAA,QACT;AAGA,YAAI,CAAC,WAAW;AACd,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,eAAe,MAAM,UAAU;AAC3C,iBAAO;AAAA,QACT;AAGA,YAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;AAC/D,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,eAAS,YAAY,WAAW;AAC9B,YAAI,WAAW,OAAO;AACtB,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,iBAAO;AAAA,QACT;AACA,YAAI,qBAAqB,QAAQ;AAI/B,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,UAAU,SAAS,GAAG;AACjC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAIA,eAAS,eAAe,WAAW;AACjC,YAAI,OAAO,cAAc,eAAe,cAAc,MAAM;AAC1D,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,UAAU;AACzB,cAAI,qBAAqB,MAAM;AAC7B,mBAAO;AAAA,UACT,WAAW,qBAAqB,QAAQ;AACtC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAIA,eAAS,yBAAyB,OAAO;AACvC,YAAI,OAAO,eAAe,KAAK;AAC/B,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,QAAQ;AAAA,UACjB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAGA,eAAS,aAAa,WAAW;AAC/B,YAAI,CAAC,UAAU,eAAe,CAAC,UAAU,YAAY,MAAM;AACzD,iBAAO;AAAA,QACT;AACA,eAAO,UAAU,YAAY;AAAA,MAC/B;AAEA,qBAAe,iBAAiB;AAChC,qBAAe,oBAAoB,eAAe;AAClD,qBAAe,YAAY;AAE3B,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjmBA;AAAA;AAOA,QAAI,MAAuC;AACrC,gBAAU;AAIV,4BAAsB;AAC1B,aAAO,UAAU,kCAAqC,QAAQ,WAAW,mBAAmB;AAAA,IAC9F,OAAO;AAGL,aAAO,UAAU,KAAsC;AAAA,IACzD;AAVM;AAIA;AAAA;AAAA;;;ACZN;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,YAAU,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,GAAE,CAAC,IAAE,YAAU,OAAO,UAAQ,QAAQ,aAAW,EAAE,IAAE,EAAE,aAAW,EAAE;AAAA,IAAC,EAAE,SAAK,WAAU;AAAC,aAAO,SAAS,GAAE;AAAC,iBAAS,EAAE,GAAE;AAAC,cAAG,EAAE,CAAC,EAAE,QAAO,EAAE,CAAC,EAAE;AAAQ,cAAI,IAAE,EAAE,CAAC,IAAE,EAAC,SAAQ,CAAC,GAAE,IAAG,GAAE,QAAO,MAAE;AAAE,iBAAO,EAAE,CAAC,EAAE,KAAK,EAAE,SAAQ,GAAE,EAAE,SAAQ,CAAC,GAAE,EAAE,SAAO,MAAG,EAAE;AAAA,QAAO;AAAC,YAAI,IAAE,CAAC;AAAE,eAAO,EAAE,IAAE,GAAE,EAAE,IAAE,GAAE,EAAE,IAAE,IAAG,EAAE,CAAC;AAAA,MAAC,EAAE,CAAC,SAAS,GAAE,GAAE;AAAC;AAAa,iBAAS,EAAEC,IAAE;AAAC,cAAIC,KAAED,GAAE,WAAW,IAAI,GAAEE,KAAEF,GAAE,OAAMG,KAAEH,GAAE,QAAOI,KAAEH,GAAE,aAAa,GAAE,GAAEC,IAAEC,EAAC,EAAE,MAAK,IAAE,EAAE,MAAGD,IAAEC,IAAEC,EAAC,GAAE,IAAE,EAAE,OAAGF,IAAEC,IAAEC,EAAC,GAAE,IAAE,EAAE,MAAGF,IAAEC,IAAEC,EAAC,GAAE,IAAE,EAAE,OAAGF,IAAEC,IAAEC,EAAC,GAAE,IAAE,IAAE,IAAE,GAAE,IAAE,IAAE,IAAE,GAAE,IAAEH,GAAE,aAAa,GAAE,GAAE,GAAE,CAAC;AAAE,iBAAOD,GAAE,QAAM,GAAEA,GAAE,SAAO,GAAEC,GAAE,UAAU,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,aAAa,GAAE,GAAE,CAAC,GAAED;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAM,EAAC,KAAIA,GAAE,KAAGD,KAAED,KAAED,GAAE,GAAE,OAAMG,GAAE,KAAGD,KAAED,KAAED,MAAG,CAAC,GAAE,MAAKG,GAAE,KAAGD,KAAED,KAAED,MAAG,CAAC,GAAE,OAAMG,GAAE,KAAGD,KAAED,KAAED,MAAG,CAAC,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAEC,IAAEE,IAAE;AAAC,iBAAO,EAAEJ,IAAEC,IAAEC,IAAEE,EAAC,EAAE;AAAA,QAAK;AAAC,iBAAS,EAAEJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAQE,KAAEL,KAAE,IAAE,IAAGM,KAAEN,KAAE,IAAEE,KAAE,GAAE,IAAEI,IAAEN,KAAE,IAAEE,KAAE,IAAE,IAAG,KAAGG,GAAE,UAAQ,IAAE,GAAE,IAAEJ,IAAE,IAAI,KAAG,EAAE,GAAE,GAAEA,IAAEE,EAAC,EAAE,QAAO;AAAE,iBAAO;AAAA,QAAI;AAAC,iBAAS,EAAEH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAQE,KAAEL,KAAE,IAAE,IAAGM,KAAEN,KAAE,IAAEC,KAAE,GAAE,IAAEK,IAAEN,KAAE,IAAEC,KAAE,IAAE,IAAG,KAAGI,GAAE,UAAQ,IAAE,GAAE,IAAEH,IAAE,IAAI,KAAG,EAAE,GAAE,GAAED,IAAEE,EAAC,EAAE,QAAO;AAAE,iBAAO;AAAA,QAAI;AAAC,eAAO,eAAe,GAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAE,EAAE,UAAQ;AAAA,MAAC,CAAC,CAAC;AAAA,IAAC,CAAC;AAAA;AAAA;;;ACAnpC,SAAS,WAAW;AAClB,SAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,IAAI,UAAU,CAAC;AACnB,eAAS,KAAK,EAAG,EAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAChE;AACA,WAAO;AAAA,EACT,GAAG,SAAS,MAAM,MAAM,SAAS;AACnC;;;ACRA,SAAS,8BAA8B,GAAG,GAAG;AAC3C,MAAI,QAAQ,EAAG,QAAO,CAAC;AACvB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,CAAC,EAAE,eAAe,KAAK,GAAG,CAAC,GAAG;AACjD,QAAI,OAAO,EAAE,QAAQ,CAAC,EAAG;AACzB,MAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EACZ;AACA,SAAO;AACT;;;ACPA,SAAS,yBAAyB,GAAG,GAAG;AACtC,MAAI,QAAQ,EAAG,QAAO,CAAC;AACvB,MAAI,GACF,GACA,IAAI,8BAA6B,GAAG,CAAC;AACvC,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,SAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,KAAI,EAAE,CAAC,GAAG,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,qBAAqB,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EACnH;AACA,SAAO;AACT;;;ACVA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,IAAI,EAAE,CAAC;AACX,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,cAAc,EAAE,GAAG,GAAG,CAAC;AAAA,EAC7I;AACF;AACA,SAAS,aAAa,GAAG,GAAG,GAAG;AAC7B,SAAO,KAAK,kBAAkB,EAAE,WAAW,CAAC,GAAG,KAAK,kBAAkB,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa;AAAA,IACjH,UAAU;AAAA,EACZ,CAAC,GAAG;AACN;;;ACXA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,MAAI,EAAE,aAAa,GAAI,OAAM,IAAI,UAAU,mCAAmC;AAChF;;;ACFA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUI,IAAGC,IAAG;AAC9F,WAAOD,GAAE,YAAYC,IAAGD;AAAA,EAC1B,GAAG,gBAAgB,GAAG,CAAC;AACzB;;;ACHA,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,cAAc,OAAO,KAAK,SAAS,EAAG,OAAM,IAAI,UAAU,oDAAoD;AAClH,IAAE,YAAY,OAAO,OAAO,KAAK,EAAE,WAAW;AAAA,IAC5C,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC,GAAG,KAAK,gBAAe,GAAG,CAAC;AAC9B;;;ACZA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUE,IAAG;AAC3F,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAC/C,GAAG,gBAAgB,CAAC;AACtB;;;ACJA,SAAS,4BAA4B;AACnC,MAAI;AACF,QAAI,IAAI,CAAC,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAY;AAAA,IAAC,CAAC,CAAC;AAAA,EACxF,SAASC,IAAG;AAAA,EAAC;AACb,UAAQ,4BAA4B,SAASC,6BAA4B;AACvE,WAAO,CAAC,CAAC;AAAA,EACX,GAAG;AACL;;;ACPA,SAAS,uBAAuB,GAAG;AACjC,MAAI,WAAW,EAAG,OAAM,IAAI,eAAe,2DAA2D;AACtG,SAAO;AACT;;;ACDA,SAAS,2BAA2B,GAAG,GAAG;AACxC,MAAI,MAAM,YAAY,QAAQ,CAAC,KAAK,cAAc,OAAO,GAAI,QAAO;AACpE,MAAI,WAAW,EAAG,OAAM,IAAI,UAAU,0DAA0D;AAChG,SAAO,uBAAsB,CAAC;AAChC;;;ACHA,SAAS,aAAa,GAAG;AACvB,MAAI,IAAI,0BAAyB;AACjC,SAAO,WAAY;AACjB,QAAI,GACF,IAAI,gBAAe,CAAC;AACtB,QAAI,GAAG;AACL,UAAI,IAAI,gBAAe,IAAI,EAAE;AAC7B,UAAI,QAAQ,UAAU,GAAG,WAAW,CAAC;AAAA,IACvC,MAAO,KAAI,EAAE,MAAM,MAAM,SAAS;AAClC,WAAO,2BAA0B,MAAM,CAAC;AAAA,EAC1C;AACF;A;;;;;;ACIA,SAAS,MAAM,GAAG,GAAG,MAAM;AACzB,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,OAAO,SAAQ,oBAAI,KAAK,GAAE,QAAQ;AACzC;AAEA,MAAM,UAAU,eAAe,SAAU,OAAO;AAC9C,SAAO,KAAK,SAAS,MAAM,OAAO,KAAK,WAAW,KAAK,KAAK,KAAK,OAAO,MAAM,QAAQ;AACxF;AAEA,MAAM,UAAU,aAAa,SAAU,OAAO;AAC5C,SAAO,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC;AAChF;AAEA,MAAM,UAAU,SAAS,SAAU,OAAO;AACxC,SAAO,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,SAAS,MAAM;AACzE;AAEA,SAAS,OAAO,YAAY,UAAU,UAAU,UAAU;AACxD,OAAK,aAAa;AAClB,OAAK,WAAW;AAChB,OAAK,WAAW;AAChB,OAAK,WAAW;AAClB;AAGA,OAAO,UAAU,SAAS,WAAY;AACpC,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAI,KAAK;AACT,MAAI,KAAK;AAET,WAAS,IAAI,GAAG,KAAK,OAAO,KAAK,GAAG;AAClC,QAAI,IAAI,IAAI;AACZ,QAAI,KAAK,KAAK,OAAO,GAAG,KAAK,WAAW,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC;AAC5F,QAAI,KAAK,KAAK,OAAO,GAAG,KAAK,WAAW,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC;AAC5F,QAAI,IAAI,GAAG;AACT,UAAI,QAAQ,KAAK;AACjB,UAAI,QAAQ,KAAK;AACjB,gBAAU,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;AAAA,IACnD;AACA,SAAK;AACL,SAAK;AAAA,EACP;AAEA,SAAO;AACT;AAGA,OAAO,UAAU,SAAS,SAAU,GAAG,OAAO,IAAI,IAAI,KAAK;AACzD,SAAO,SAAS,IAAM,MAAM,IAAM,MAAM,IAAM,KAAK,IAAM,MAAM,IAAM,MAAM,IAAM,KAAK,IAAI,IAAM,MAAM,IAAM,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI;AACzI;AAKA,SAAS,SAAS,MAAM,MAAM,SAAS;AACrC,MAAI,SAAS,MAAM;AACnB,MAAI,UAAU;AACd,MAAI,WAAW;AACf,MAAI,CAAC,QAAS,WAAU,CAAC;AACzB,MAAI,QAAQ,SAASC,SAAQ;AAC3B,eAAW,QAAQ,YAAY,QAAQ,IAAI,KAAK,IAAI;AACpD,cAAU;AACV,aAAS,KAAK,MAAM,SAAS,IAAI;AACjC,QAAI,CAAC,QAAS,WAAU,OAAO;AAAA,EACjC;AACA,SAAO,WAAY;AACjB,QAAI,MAAM,KAAK,IAAI;AACnB,QAAI,CAAC,YAAY,QAAQ,YAAY,MAAO,YAAW;AACvD,QAAI,YAAY,QAAQ,MAAM;AAC9B,cAAU;AACV,WAAO;AACP,QAAI,aAAa,KAAK,YAAY,MAAM;AACtC,UAAI,SAAS;AACX,qBAAa,OAAO;AACpB,kBAAU;AAAA,MACZ;AACA,iBAAW;AACX,eAAS,KAAK,MAAM,SAAS,IAAI;AACjC,UAAI,CAAC,QAAS,WAAU,OAAO;AAAA,IACjC,WAAW,CAAC,WAAW,QAAQ,aAAa,OAAO;AACjD,gBAAU,WAAW,OAAO,SAAS;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,QAAQ,SAAS;AACrC,MAAI,OAAO;AACX,MAAI,OAAO,WAAW,CAAC;AAEvB,OAAK,uBAAuB,KAAK,wBAAwB;AACzD,OAAK,WAAW,KAAK,YAAY;AACjC,OAAK,WAAW,KAAK,YAAY;AACjC,OAAK,WAAW,cAAc,OAAO,KAAK,WAAW;AACrD,OAAK,cAAc,iBAAiB,OAAO,KAAK,cAAc;AAE9D,MAAI,KAAK,UAAU;AACjB,SAAK,oBAAoB,SAAS,aAAa,UAAU,eAAe,KAAK,QAAQ;AAAA,EACvF,OAAO;AACL,SAAK,oBAAoB,aAAa,UAAU;AAAA,EAClD;AAEA,OAAK,UAAU,KAAK,WAAW,WAAY;AACzC,YAAQ,KAAK,WAAW,KAAK,YAAY;AAAA,EAC3C;AACA,OAAK,WAAW,KAAK,YAAY;AACjC,OAAK,kBAAkB,KAAK,mBAAmB;AAC/C,OAAK,UAAU,KAAK;AACpB,OAAK,QAAQ,KAAK;AAElB,OAAK,UAAU;AACf,OAAK,OAAO,OAAO,WAAW,IAAI;AAClC,OAAK,MAAM;AAIX,OAAK,mBAAmB,SAAU,OAAO;AACvC,QAAI,MAAM,UAAU,GAAG;AACrB,WAAK,mBAAmB;AACxB,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAEA,OAAK,mBAAmB,SAAU,OAAO;AACvC,QAAI,KAAK,kBAAkB;AACzB,WAAK,kBAAkB,KAAK;AAAA,IAC9B;AAAA,EACF;AAEA,OAAK,iBAAiB,SAAU,OAAO;AACrC,QAAI,MAAM,UAAU,KAAK,KAAK,kBAAkB;AAC9C,WAAK,mBAAmB;AACxB,WAAK,WAAW,KAAK;AAAA,IACvB;AAAA,EACF;AAEA,OAAK,oBAAoB,SAAU,OAAO;AACxC,QAAI,MAAM,cAAc,WAAW,GAAG;AACpC,UAAI,QAAQ,MAAM,eAAe,CAAC;AAClC,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAEA,OAAK,mBAAmB,SAAU,OAAO;AAEvC,UAAM,eAAe;AAErB,QAAI,QAAQ,MAAM,cAAc,CAAC;AACjC,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAEA,OAAK,kBAAkB,SAAU,OAAO;AACtC,QAAI,mBAAmB,MAAM,WAAW,KAAK;AAC7C,QAAI,kBAAkB;AACpB,YAAM,eAAe;AACrB,WAAK,WAAW,KAAK;AAAA,IACvB;AAAA,EACF;AAGA,OAAK,GAAG;AACV;AAGA,aAAa,UAAU,QAAQ,WAAY;AACzC,MAAI,MAAM,KAAK;AACf,MAAI,SAAS,KAAK;AAElB,MAAI,YAAY,KAAK;AACrB,MAAI,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC/C,MAAI,SAAS,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAE9C,OAAK,QAAQ,CAAC;AACd,OAAK,OAAO;AACZ,OAAK,WAAW;AAClB;AAEA,aAAa,UAAU,cAAc,SAAU,SAAS;AACtD,MAAI,QAAQ;AAEZ,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,MAAI,QAAQ,IAAI,MAAM;AACtB,MAAI,QAAQ,QAAQ,SAAS,OAAO,oBAAoB;AACxD,MAAI,QAAQ,QAAQ,SAAS,KAAK,QAAQ,QAAQ;AAClD,MAAI,SAAS,QAAQ,UAAU,KAAK,QAAQ,SAAS;AAErD,OAAK,OAAO;AACZ,QAAM,MAAM;AACZ,QAAM,SAAS,WAAY;AACzB,UAAM,KAAK,UAAU,OAAO,GAAG,GAAG,OAAO,MAAM;AAAA,EACjD;AACA,OAAK,WAAW;AAClB;AAEA,aAAa,UAAU,YAAY,SAAU,MAAM;AACjD,MAAI;AAEJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,KAAK,OAAO;AAAA,IACrB;AACE,eAAS,OAAO,UAAU,QAAQ,UAAU,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACzG,gBAAQ,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,MACpC;AAEA,cAAQ,UAAU,KAAK,SAAS,UAAU,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,OAAO,CAAC;AAAA,EACnF;AACF;AAEA,aAAa,UAAU,KAAK,WAAY;AACtC,OAAK,mBAAmB;AACxB,OAAK,mBAAmB;AAC1B;AAEA,aAAa,UAAU,MAAM,WAAY;AACvC,OAAK,QAAQ,oBAAoB,aAAa,KAAK,gBAAgB;AACnE,OAAK,QAAQ,oBAAoB,aAAa,KAAK,gBAAgB;AACnE,WAAS,oBAAoB,WAAW,KAAK,cAAc;AAE3D,OAAK,QAAQ,oBAAoB,cAAc,KAAK,iBAAiB;AACrE,OAAK,QAAQ,oBAAoB,aAAa,KAAK,gBAAgB;AACnE,OAAK,QAAQ,oBAAoB,YAAY,KAAK,eAAe;AACnE;AAEA,aAAa,UAAU,UAAU,WAAY;AAC3C,SAAO,KAAK;AACd;AAGA,aAAa,UAAU,eAAe,SAAU,OAAO;AACrD,OAAK,MAAM,KAAK,CAAC,CAAC;AAClB,OAAK,OAAO;AACZ,OAAK,cAAc,KAAK;AAExB,MAAI,OAAO,KAAK,YAAY,YAAY;AACtC,SAAK,QAAQ,KAAK;AAAA,EACpB;AACF;AAEA,aAAa,UAAU,gBAAgB,SAAU,OAAO;AACtD,MAAI,IAAI,MAAM;AACd,MAAI,IAAI,MAAM;AAEd,MAAI,QAAQ,KAAK,aAAa,GAAG,CAAC;AAClC,MAAI,iBAAiB,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACrD,MAAI,YAAY,kBAAkB,eAAe,eAAe,SAAS,CAAC;AAC1E,MAAI,sBAAsB,aAAa,MAAM,WAAW,SAAS,IAAI,KAAK;AAG1E,MAAI,EAAE,aAAa,sBAAsB;AACvC,QAAI,YAAY,KAAK,UAAU,KAAK,GAChC,QAAQ,UAAU,OAClB,SAAS,UAAU;AAEvB,QAAI,SAAS,QAAQ;AACnB,WAAK,WAAW,OAAO,OAAO,OAAO,OAAO,GAAG;AAAA,IACjD;AAEA,SAAK,MAAM,KAAK,MAAM,SAAS,CAAC,EAAE,KAAK;AAAA,MACrC,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,MACT,MAAM,MAAM;AAAA,MACZ,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAEA,aAAa,UAAU,aAAa,SAAU,OAAO;AACnD,MAAI,eAAe,KAAK,OAAO,SAAS;AACxC,MAAI,QAAQ,KAAK,OAAO,CAAC;AAEzB,MAAI,CAAC,gBAAgB,OAAO;AAC1B,SAAK,SAAS,KAAK;AAAA,EACrB;AAEA,MAAI,OAAO;AACT,QAAI,iBAAiB,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACrD,QAAI,YAAY,eAAe,eAAe,SAAS,CAAC;AAIxD,QAAI,CAAC,MAAM,OAAO,SAAS,GAAG;AAC5B,qBAAe,KAAK;AAAA,QAClB,GAAG,MAAM;AAAA,QACT,GAAG,MAAM;AAAA,QACT,MAAM,MAAM;AAAA,QACZ,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,OAAO,KAAK,UAAU,YAAY;AACpC,SAAK,MAAM,KAAK;AAAA,EAClB;AACF;AAEA,aAAa,UAAU,qBAAqB,WAAY;AACtD,OAAK,mBAAmB;AAExB,OAAK,QAAQ,iBAAiB,aAAa,KAAK,gBAAgB;AAChE,OAAK,QAAQ,iBAAiB,aAAa,KAAK,gBAAgB;AAChE,WAAS,iBAAiB,WAAW,KAAK,cAAc;AAC1D;AAEA,aAAa,UAAU,qBAAqB,WAAY;AAEtD,OAAK,QAAQ,MAAM,gBAAgB;AACnC,OAAK,QAAQ,MAAM,cAAc;AAEjC,OAAK,QAAQ,iBAAiB,cAAc,KAAK,iBAAiB;AAClE,OAAK,QAAQ,iBAAiB,aAAa,KAAK,gBAAgB;AAChE,OAAK,QAAQ,iBAAiB,YAAY,KAAK,eAAe;AAChE;AAEA,aAAa,UAAU,SAAS,WAAY;AAC1C,OAAK,SAAS,CAAC;AACf,OAAK,gBAAgB;AACrB,OAAK,cAAc,KAAK,WAAW,KAAK,YAAY;AACpD,OAAK,KAAK,YAAY,KAAK;AAC7B;AAEA,aAAa,UAAU,eAAe,SAAU,GAAG,GAAG,MAAM;AAC1D,MAAI,OAAO,KAAK,QAAQ,sBAAsB;AAE9C,SAAO,IAAI,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK,SAAQ,oBAAI,KAAK,GAAE,QAAQ,CAAC;AAC5E;AAEA,aAAa,UAAU,YAAY,SAAU,OAAO;AAClD,MAAI,SAAS,KAAK;AAClB,MAAI,MAAM;AAEV,SAAO,KAAK,KAAK;AAEjB,MAAI,OAAO,SAAS,GAAG;AAGrB,QAAI,OAAO,WAAW,EAAG,QAAO,QAAQ,OAAO,CAAC,CAAC;AAEjD,UAAM,KAAK,6BAA6B,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACvE,QAAI,KAAK,IAAI;AACb,UAAM,KAAK,6BAA6B,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACvE,QAAI,KAAK,IAAI;AACb,QAAI,QAAQ,IAAI,OAAO,OAAO,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC,CAAC;AACnD,QAAI,SAAS,KAAK,sBAAsB,KAAK;AAI7C,WAAO,MAAM;AAEb,WAAO,EAAE,OAAc,OAAe;AAAA,EACxC;AAEA,SAAO,CAAC;AACV;AAEA,aAAa,UAAU,+BAA+B,SAAU,IAAI,IAAI,IAAI;AAC1E,MAAI,MAAM,GAAG,IAAI,GAAG;AACpB,MAAI,MAAM,GAAG,IAAI,GAAG;AACpB,MAAI,MAAM,GAAG,IAAI,GAAG;AACpB,MAAI,MAAM,GAAG,IAAI,GAAG;AAEpB,MAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,KAAK,GAAK,IAAI,GAAG,IAAI,GAAG,KAAK,EAAI;AAC1D,MAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,KAAK,GAAK,IAAI,GAAG,IAAI,GAAG,KAAK,EAAI;AAE1D,MAAI,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AACxC,MAAI,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAExC,MAAI,MAAM,GAAG,IAAI,GAAG;AACpB,MAAI,MAAM,GAAG,IAAI,GAAG;AAEpB,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,KAAK,EAAE,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,GAAG,IAAI,MAAM,EAAE;AAEhD,MAAI,KAAK,GAAG,IAAI,GAAG;AACnB,MAAI,KAAK,GAAG,IAAI,GAAG;AAEnB,SAAO;AAAA,IACL,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,IAClC,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,EACpC;AACF;AAEA,aAAa,UAAU,wBAAwB,SAAU,OAAO;AAC9D,MAAI,aAAa,MAAM;AACvB,MAAI,WAAW,MAAM;AACrB,MAAI,SAAS,EAAE,OAAO,MAAM,KAAK,KAAK;AAEtC,MAAI,WAAW,KAAK,uBAAuB,SAAS,aAAa,UAAU,KAAK,IAAI,KAAK,wBAAwB,KAAK;AAEtH,MAAI,WAAW,KAAK,aAAa,QAAQ;AAEzC,SAAO,QAAQ,KAAK;AACpB,SAAO,MAAM;AAEb,OAAK,gBAAgB;AACrB,OAAK,aAAa;AAElB,SAAO;AACT;AAEA,aAAa,UAAU,eAAe,SAAU,UAAU;AACxD,SAAO,KAAK,IAAI,KAAK,YAAY,WAAW,IAAI,KAAK,QAAQ;AAC/D;AAEA,aAAa,UAAU,aAAa,SAAU,GAAG,GAAG,MAAM;AACxD,MAAI,MAAM,KAAK;AAEf,MAAI,OAAO,GAAG,CAAC;AACf,MAAI,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,KAAK;AACzC,OAAK,WAAW;AAClB;AAEA,aAAa,UAAU,aAAa,SAAU,OAAO,YAAY,UAAU;AACzE,MAAI,MAAM,KAAK;AACf,MAAI,aAAa,WAAW;AAC5B,MAAI,YAAY,KAAK,MAAM,MAAM,OAAO,CAAC;AAEzC,MAAI,UAAU;AAEd,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK,GAAG;AAErC,QAAI,IAAI,IAAI;AACZ,QAAI,KAAK,IAAI;AACb,QAAI,MAAM,KAAK;AACf,QAAI,IAAI,IAAI;AACZ,QAAI,KAAK,IAAI;AACb,QAAI,MAAM,KAAK;AAEf,QAAI,IAAI,MAAM,MAAM,WAAW;AAC/B,SAAK,IAAI,KAAK,IAAI,MAAM,SAAS;AACjC,SAAK,IAAI,IAAI,KAAK,MAAM,SAAS;AACjC,SAAK,MAAM,MAAM,SAAS;AAE1B,QAAI,IAAI,MAAM,MAAM,WAAW;AAC/B,SAAK,IAAI,KAAK,IAAI,MAAM,SAAS;AACjC,SAAK,IAAI,IAAI,KAAK,MAAM,SAAS;AACjC,SAAK,MAAM,MAAM,SAAS;AAE1B,QAAI,QAAQ,aAAa,MAAM;AAC/B,SAAK,WAAW,GAAG,GAAG,KAAK;AAAA,EAC7B;AAEA,MAAI,UAAU;AACd,MAAI,KAAK;AACX;AAEA,aAAa,UAAU,WAAW,SAAU,OAAO;AACjD,MAAI,MAAM,KAAK;AACf,MAAI,QAAQ,OAAO,KAAK,YAAY,aAAa,KAAK,QAAQ,IAAI,KAAK;AAEvE,MAAI,UAAU;AACd,OAAK,WAAW,MAAM,GAAG,MAAM,GAAG,KAAK;AACvC,MAAI,UAAU;AACd,MAAI,KAAK;AACX;AAEA,aAAa,UAAU,YAAY,SAAU,aAAa,WAAW,SAAS;AAC5E,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK,GAAG;AAC9C,QAAI,QAAQ,YAAY,CAAC;AAEzB,QAAI,MAAM,SAAS,GAAG;AACpB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,YAAI,WAAW,MAAM,CAAC;AACtB,YAAI,QAAQ,IAAI,MAAM,SAAS,GAAG,SAAS,GAAG,SAAS,IAAI;AAC3D,YAAI,QAAQ,SAAS;AAErB,YAAI,MAAM,GAAG;AAKX,eAAK,WAAW;AAChB,eAAK,OAAO;AAEZ,eAAK,UAAU,KAAK;AAAA,QACtB,WAAW,MAAM,MAAM,SAAS,GAAG;AAEjC,cAAI,aAAa,KAAK,UAAU,KAAK,GACjC,QAAQ,WAAW,OACnB,SAAS,WAAW;AAExB,cAAI,SAAS,QAAQ;AACnB,sBAAU,OAAO,QAAQ,KAAK;AAAA,UAChC;AAAA,QACF,OAAO;AAAA,QAEP;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,OAAO;AACZ,UAAI,YAAY,MAAM,CAAC;AACvB,cAAQ,SAAS;AAAA,IACnB;AAAA,EACF;AACF;AAEA,aAAa,UAAU,SAAS,WAAY;AAC1C,MAAI,SAAS;AAEb,MAAI,cAAc,KAAK;AACvB,MAAI,SAAS,KAAK;AAClB,MAAI,QAAQ,KAAK,IAAI,OAAO,oBAAoB,GAAG,CAAC;AACpD,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,OAAO,OAAO,QAAQ;AAC1B,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,MAAM,SAAS,gBAAgB,8BAA8B,KAAK;AAEtE,MAAI,eAAe,MAAM,SAAS,OAAO,KAAK;AAC9C,MAAI,eAAe,MAAM,UAAU,OAAO,MAAM;AAEhD,OAAK,UAAU,aAAa,SAAU,OAAO,QAAQ,OAAO;AAC1D,QAAI,OAAO,SAAS,cAAc,MAAM;AAKxC,QAAI,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,MAAM,SAAS,CAAC,GAAG;AAChH,UAAI,OAAO,OAAO,MAAM,WAAW,EAAE,QAAQ,CAAC,IAAI,MAAM,MAAM,WAAW,EAAE,QAAQ,CAAC,IAAI,OAAO,OAAO,MAAM,SAAS,EAAE,QAAQ,CAAC,IAAI,MAAM,MAAM,SAAS,EAAE,QAAQ,CAAC,IAAI,QAAQ,MAAM,SAAS,EAAE,QAAQ,CAAC,IAAI,MAAM,MAAM,SAAS,EAAE,QAAQ,CAAC,IAAI,QAAQ,MAAM,SAAS,EAAE,QAAQ,CAAC,IAAI,MAAM,MAAM,SAAS,EAAE,QAAQ,CAAC;AAExT,WAAK,aAAa,KAAK,IAAI;AAC3B,WAAK,aAAa,iBAAiB,OAAO,MAAM,MAAM,QAAQ,CAAC,CAAC;AAChE,WAAK,aAAa,UAAU,KAAK;AACjC,WAAK,aAAa,QAAQ,MAAM;AAChC,WAAK,aAAa,kBAAkB,OAAO;AAE3C,UAAI,YAAY,IAAI;AAAA,IACtB;AAAA,EACF,GAAG,SAAU,UAAU;AACrB,QAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,QAAI,UAAU,OAAO,OAAO,YAAY,aAAa,OAAO,QAAQ,IAAI,OAAO;AAC/E,WAAO,aAAa,KAAK,OAAO;AAChC,WAAO,aAAa,MAAM,SAAS,CAAC;AACpC,WAAO,aAAa,MAAM,SAAS,CAAC;AACpC,WAAO,aAAa,QAAQ,SAAS,KAAK;AAE1C,QAAI,YAAY,MAAM;AAAA,EACxB,CAAC;AAED,MAAI,SAAS;AACb,MAAI,SAAS,wFAAkG,eAAe,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,QAAQ,aAAa,OAAO,QAAQ,cAAc,OAAO,OAAO;AAC5O,MAAI,OAAO,IAAI;AAGf,MAAI,SAAS,QAAW;AACtB,QAAI,QAAQ,SAAS,cAAc,OAAO;AAC1C,QAAI,QAAQ,IAAI;AAChB,UAAM,YAAY;AAElB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,YAAM,YAAY,MAAM,CAAC,EAAE,UAAU,IAAI,CAAC;AAAA,IAC5C;AAEA,WAAO,MAAM;AAAA,EACf;AAEA,MAAI,SAAS;AACb,MAAI,OAAO,SAAS,OAAO;AAE3B,SAAO,SAAS,KAAK,IAAI;AAC3B;AAEA,aAAa,UAAU,WAAW,SAAU,aAAa;AACvD,MAAI,SAAS;AAEb,OAAK,MAAM;AAEX,OAAK,UAAU,aAAa,SAAU,OAAO,QAAQ;AACnD,WAAO,OAAO,WAAW,OAAO,OAAO,OAAO,OAAO,GAAG;AAAA,EAC1D,GAAG,SAAU,UAAU;AACrB,WAAO,OAAO,SAAS,QAAQ;AAAA,EACjC,CAAC;AAED,OAAK,QAAQ;AACf;AAEA,aAAa,UAAU,SAAS,WAAY;AAC1C,SAAO,KAAK;AACd;AAEA,IAAO,wBAAQ;;;;;AC/kBFC,IAAAA,kBAAe,SAAAC,YAAA;AAAAC,YAAAF,kBAAAC,UAAA;AAAA,MAAAE,SAAAC,aAAAJ,gBAAA;AAAA,WAAAA,mBAAA;AAAA,QAAAK;AAAAC,oBAAA,MAAAN,gBAAA;AAAA,aAAAO,OAAAC,UAAAC,QAAAC,OAAAC,IAAAA,MAAAJ,IAAA,GAAAK,OAAA,GAAAA,OAAAL,MAAAK,QAAA;AAAAF,WAAAE,IAAA,IAAAJ,UAAAI,IAAA;IAAA;AAAAP,YAAAF,OAAAU,KAAAC,MAAAX,QAAA,CAAA,IAAA,EAAAY,OAAAL,IAAA,CAAA;AAAAL,UAyBTW,aAAaX,MAAKY;AAAWZ,UAE9Ca,UAA+B;AAAIb,UACnCc,UAAoC;AAAId,UAEvBe,SAAS,SAACC,KAAwC;AACjEhB,YAAKc,UAAUE;AAEf,UAAIhB,MAAKc,YAAY,MAAM;AACzBd,cAAKa,UAAU;MACjB;;AACDb,UAEDiB,mBAAmB,WAAwC;AACzD,UAAAC,cAAuDlB,MAAKmB;AAAzCD,kBAAXE;AAA0BF,kBAAbG;AAAkBC,UAAAA,cAAWC,yBAAAL,aAAAM,SAAA;AAClD,aAAOF;;AACRtB,UAEQyB,oBAAoD,WAAM;AACjE,UAAMC,SAAS1B,MAAK2B,UAAS;AAC7B3B,YAAKa,UAAU,IAAIe,sBAAaF,QAAQ1B,MAAKiB,iBAAgB,CAAE;AAC/DjB,YAAK6B,cAAa;AAClB7B,YAAK8B,GAAE;;AACR9B,UAEQ+B,uBAA0D,WAAM;AACvE/B,YAAKgC,IAAG;;AACThC,UAGQiC,qBAAsD,WAAM;AACnEC,aAAOC,OAAOnC,MAAKa,SAASb,MAAKiB,iBAAgB,CAAE;;AACpDjB,UAGD2B,YAAY,WAAyB;AACnC,UAAI3B,MAAKc,YAAY,MAAM;AACzB,cAAMd,MAAKW,WAAWyB;MACxB;AACA,aAAOpC,MAAKc;;AACbd,UAGDqC,mBAAmB,WAAyB;AAE1C,UAAMX,SAAS1B,MAAK2B,UAAS;AAC7B,UAAMW,OAAOC,SAASC,cAAc,QAAQ;AAC5CF,WAAKG,QAAQf,OAAOe;AACpBH,WAAKI,SAAShB,OAAOgB;AAErBJ,WAAKK,WAAW,IAAI,EAAGC,UAAUlB,QAAQ,GAAG,CAAC;AAE7C,iBAAOmB,mBAAAA,SAAWP,IAAI;;AACvBtC,UAGD8C,kBAAkB,WAAoB;AACpC,UAAI9C,MAAKa,YAAY,MAAM;AACzB,cAAMb,MAAKW,WAAWyB;MACxB;AACA,aAAOpC,MAAKa;;AACbb,UAED+C,sBAAsB,WAAY;AAChC,UAAI,CAAC/C,MAAKmB,MAAME,eAAe;AAC7B;MACF;AACArB,YAAK6B,cAAa;;AACnB7B,UAED6B,gBAAgB,WAAY;AAAA,UAAAmB,uBAAAC;AAC1B,UAAM7B,eAAW4B,wBAAGhD,MAAKmB,MAAMC,iBAAW,QAAA4B,0BAAA,SAAAA,wBAAI,CAAA;AAC9C,UAAQP,QAAkBrB,YAAlBqB,OAAOC,SAAWtB,YAAXsB;AAEf,UAAI,OAAOD,UAAU,eAAe,OAAOC,WAAW,aAAa;AACjE;MACF;AAEA,UAAMhB,SAAS1B,MAAK2B,UAAS;AAI7B,UAAMuB,QAAQC,KAAKC,KAAGH,wBAACI,OAAOC,sBAAgB,QAAAL,0BAAAA,SAAAA,wBAAI,GAAG,CAAC;AAEtD,UAAI,OAAOR,UAAU,aAAa;AAChCf,eAAOe,QAAQf,OAAO6B,cAAcL;MACtC;AACA,UAAI,OAAOR,WAAW,aAAa;AACjChB,eAAOgB,SAAShB,OAAO8B,eAAeN;MACxC;AAEAxB,aAAOiB,WAAW,IAAI,EAAGc,MAAMP,OAAOA,KAAK;AAC3ClD,YAAK0D,MAAK;;AACX1D,UAEQ2D,SAA8B,WAAM;AAC3C,UAAQvC,cAAgBpB,MAAKmB,MAArBC;AACR,aAAOwC,aAAAA,QAAApB,cAAA,UAAAqB,SAAA;QAAQ7C,KAAKhB,MAAKe;SAAYK,WAAW,CAAG;;AACpDpB,UAID8B,KAAyB,WAAM;AAC7BuB,aAAOS,iBAAiB,UAAU9D,MAAK+C,mBAAmB;AAC1D,aAAO/C,MAAK8C,gBAAe,EAAGhB,GAAE;;AACjC9B,UAEDgC,MAA2B,WAAM;AAC/BqB,aAAOU,oBAAoB,UAAU/D,MAAK+C,mBAAmB;AAC7D,aAAO/C,MAAK8C,gBAAe,EAAGd,IAAG;;AAClChC,UAED0D,QAA+B,WAAM;AACnC,aAAO1D,MAAK8C,gBAAe,EAAGY,MAAK;;AACpC1D,UAEDgE,UAAmC,WAAM;AACvC,aAAOhE,MAAK8C,gBAAe,EAAGkB,QAAO;;AACtChE,UAEDiE,cAA2C,SAACC,SAASC,SAAY;AAC/D,aAAOnE,MAAK8C,gBAAe,EAAGmB,YAAYC,SAASC,OAAO;;AAC3DnE,UAEDoE,YAAuC,SAACC,MAAMC,gBAAmB;AAC/D,aAAOtE,MAAK8C,gBAAe,EAAGsB,UAAUC,MAAMC,cAAc;;AAC7DtE,UAEDuE,WAAqC,SAACC,aAAgB;AACpD,aAAOxE,MAAK8C,gBAAe,EAAGyB,SAASC,WAAW;;AACnDxE,UAEDyE,SAAiC,WAAM;AACrC,aAAOzE,MAAK8C,gBAAe,EAAG2B,OAAM;;AACrC,WAAAzE;;AAjCD,SAAA0E,aAAA/E,gBAAA;AAAA,EA9HmCgF,sBAAS;AAAjChF,gBACKiF,YAAY;;EAE1BC,sBAAsBC,kBAAAA,QAAUC;EAChCC,UAAUF,kBAAAA,QAAUC;EACpBE,UAAUH,kBAAAA,QAAUC;EACpBG,aAAaJ,kBAAAA,QAAUC;EACvBI,SAASL,kBAAAA,QAAUM,UAAU,CAACN,kBAAAA,QAAUC,QAAQD,kBAAAA,QAAUO,IAAI,CAAC;EAC/DC,UAAUR,kBAAAA,QAAUS;EACpBC,UAAUV,kBAAAA,QAAUC;EACpBU,OAAOX,kBAAAA,QAAUO;EACjBK,SAASZ,kBAAAA,QAAUO;;EAEnBjE,aAAa0D,kBAAAA,QAAUa;EACvBtE,eAAeyD,kBAAAA,QAAUc;AAC3B;AAfWjG,gBAiBJkG,eAA4D;EACjExE,eAAe;AACjB;AAnBW1B,gBAqBJyC,eAAe,IAAI0D,MAAM,oGACkC;", "names": ["i", "checker", "e", "t", "r", "n", "o", "a", "u", "t", "e", "t", "t", "_isNativeReflectConstruct", "later", "SignatureCanvas", "_Component", "_inherits", "_super", "_createSuper", "_this", "_classCallCheck", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "staticThis", "constructor", "_sigPad", "_canvas", "setRef", "ref", "_excludeOurProps", "_this$props", "props", "canvasProps", "clearOnResize", "sigPadProps", "_objectWithoutProperties", "_excluded", "componentDidMount", "canvas", "get<PERSON>anvas", "SignaturePad", "_resizeCanvas", "on", "componentWillUnmount", "off", "componentDidUpdate", "Object", "assign", "refNull<PERSON><PERSON>r", "getTrimmedCanvas", "copy", "document", "createElement", "width", "height", "getContext", "drawImage", "trimCanvas", "getSignaturePad", "_checkClearOnResize", "_this$props$canvasPro", "_window$devicePixelRa", "ratio", "Math", "max", "window", "devicePixelRatio", "offsetWidth", "offsetHeight", "scale", "clear", "render", "React", "_extends", "addEventListener", "removeEventListener", "isEmpty", "fromDataURL", "dataURL", "options", "toDataURL", "type", "encoderOptions", "fromData", "pointGroups", "toData", "_createClass", "Component", "propTypes", "velocityFilterWeight", "PropTypes", "number", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minDistance", "dotSize", "oneOfType", "func", "penColor", "string", "throttle", "onEnd", "onBegin", "object", "bool", "defaultProps", "Error"]}