{"name": "signature_pad", "description": "Library for drawing smooth signatures.", "version": "2.3.2", "homepage": "https://github.com/szimek/signature_pad", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/szimek"}, "license": "MIT", "main": "dist/signature_pad.js", "module": "dist/signature_pad.mjs", "scripts": {"build": "gulp", "watch": "gulp watch", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "https://github.com/szimek/signature_pad.git"}, "files": ["src", "dist", "example"], "devDependencies": {"babel-plugin-transform-es2015-modules-umd": "^6.18.0", "babel-preset-es2015": "^6.18.0", "del": "^2.2.2", "eslint": "^3.13.1", "eslint-config-airbnb-base": "^11.0.1", "eslint-plugin-import": "^2.2.0", "gulp": "^3.9.1", "rollup": "^0.41.4", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-eslint": "^3.0.0", "rollup-plugin-uglify": "^1.0.1"}}