!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@babel/runtime/helpers/extends"),require("@babel/runtime/helpers/objectWithoutProperties"),require("@babel/runtime/helpers/createClass"),require("@babel/runtime/helpers/classCallCheck"),require("@babel/runtime/helpers/inherits"),require("@babel/runtime/helpers/createSuper"),require("prop-types"),require("react"),require("signature_pad"),require("trim-canvas")):"function"==typeof define&&define.amd?define(["exports","@babel/runtime/helpers/extends","@babel/runtime/helpers/objectWithoutProperties","@babel/runtime/helpers/createClass","@babel/runtime/helpers/classCallCheck","@babel/runtime/helpers/inherits","@babel/runtime/helpers/createSuper","prop-types","react","signature_pad","trim-canvas"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).SignatureCanvas={},e._extends,e._objectWithoutProperties,e._createClass,e._classCallCheck,e._inherits,e._createSuper,e.PropTypes,e.React,e.SignaturePad,e.trimCanvas)}(this,(function(e,t,r,n,a,i,u,s,o,l,c){"use strict";function d(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var f=d(t),p=d(r),h=d(n),g=d(a),v=d(i),m=d(u),b=d(s),P=d(o),_=d(l),C=d(c),y=["canvasProps","clearOnResize"],R=function(e){v.default(r,e);var t=m.default(r);function r(){var e;g.default(this,r);for(var n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return(e=t.call.apply(t,[this].concat(a))).staticThis=e.constructor,e._sigPad=null,e._canvas=null,e.setRef=function(t){e._canvas=t,null===e._canvas&&(e._sigPad=null)},e._excludeOurProps=function(){var t=e.props;return t.canvasProps,t.clearOnResize,p.default(t,y)},e.componentDidMount=function(){var t=e.getCanvas();e._sigPad=new _.default(t,e._excludeOurProps()),e._resizeCanvas(),e.on()},e.componentWillUnmount=function(){e.off()},e.componentDidUpdate=function(){Object.assign(e._sigPad,e._excludeOurProps())},e.getCanvas=function(){if(null===e._canvas)throw e.staticThis.refNullError;return e._canvas},e.getTrimmedCanvas=function(){var t=e.getCanvas(),r=document.createElement("canvas");return r.width=t.width,r.height=t.height,r.getContext("2d").drawImage(t,0,0),C.default(r)},e.getSignaturePad=function(){if(null===e._sigPad)throw e.staticThis.refNullError;return e._sigPad},e._checkClearOnResize=function(){e.props.clearOnResize&&e._resizeCanvas()},e._resizeCanvas=function(){var t,r,n=null!==(t=e.props.canvasProps)&&void 0!==t?t:{},a=n.width,i=n.height;if(void 0===a||void 0===i){var u=e.getCanvas(),s=Math.max(null!==(r=window.devicePixelRatio)&&void 0!==r?r:1,1);void 0===a&&(u.width=u.offsetWidth*s),void 0===i&&(u.height=u.offsetHeight*s),u.getContext("2d").scale(s,s),e.clear()}},e.render=function(){var t=e.props.canvasProps;return P.default.createElement("canvas",f.default({ref:e.setRef},t))},e.on=function(){return window.addEventListener("resize",e._checkClearOnResize),e.getSignaturePad().on()},e.off=function(){return window.removeEventListener("resize",e._checkClearOnResize),e.getSignaturePad().off()},e.clear=function(){return e.getSignaturePad().clear()},e.isEmpty=function(){return e.getSignaturePad().isEmpty()},e.fromDataURL=function(t,r){return e.getSignaturePad().fromDataURL(t,r)},e.toDataURL=function(t,r){return e.getSignaturePad().toDataURL(t,r)},e.fromData=function(t){return e.getSignaturePad().fromData(t)},e.toData=function(){return e.getSignaturePad().toData()},e}return h.default(r)}(o.Component);R.propTypes={velocityFilterWeight:b.default.number,minWidth:b.default.number,maxWidth:b.default.number,minDistance:b.default.number,dotSize:b.default.oneOfType([b.default.number,b.default.func]),penColor:b.default.string,throttle:b.default.number,onEnd:b.default.func,onBegin:b.default.func,canvasProps:b.default.object,clearOnResize:b.default.bool},R.defaultProps={clearOnResize:!0},R.refNullError=new Error("react-signature-canvas is currently mounting or unmounting: React refs are null during this phase."),e.SignatureCanvas=R,e.default=R,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=index.umd.min.js.map
