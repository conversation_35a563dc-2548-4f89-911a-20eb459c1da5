{"version": 3, "file": "index.umd.min.js", "sources": ["../src/index.tsx"], "sourcesContent": null, "names": ["SignatureCanvas", "_Component", "_inherits", "_super", "_createSuper", "_this", "_classCallCheck", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "this", "concat", "staticThis", "constructor", "_sigPad", "_canvas", "setRef", "ref", "_excludeOurProps", "_this$props", "props", "canvasProps", "clearOnResize", "_objectWithoutProperties", "_excluded", "componentDidMount", "canvas", "get<PERSON>anvas", "SignaturePad", "_resizeCanvas", "on", "componentWillUnmount", "off", "componentDidUpdate", "Object", "assign", "refNull<PERSON><PERSON>r", "getTrimmedCanvas", "copy", "document", "createElement", "width", "height", "getContext", "drawImage", "trimCanvas", "getSignaturePad", "_checkClearOnResize", "_this$props$canvasPro", "_window$devicePixelRa", "ratio", "Math", "max", "window", "devicePixelRatio", "offsetWidth", "offsetHeight", "scale", "clear", "render", "React", "_extends", "addEventListener", "removeEventListener", "isEmpty", "fromDataURL", "dataURL", "options", "toDataURL", "type", "encoderOptions", "fromData", "pointGroups", "toData", "_createClass", "Component", "propTypes", "velocityFilterWeight", "PropTypes", "number", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minDistance", "dotSize", "oneOfType", "func", "penColor", "string", "throttle", "onEnd", "onBegin", "object", "bool", "defaultProps", "Error"], "mappings": "6tCAUaA,WAAeC,GAAAC,UAAAF,EAAAC,GAAA,IAAAE,EAAAC,UAAAJ,GAAA,SAAAA,IAAA,IAAAK,EAAAC,eAAAN,GAAA,IAAA,IAAAO,EAAAC,UAAAC,OAAAC,EAAAC,IAAAA,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IAAAF,EAAAE,GAAAJ,UAAAI,GA+JzB,OA/JyBP,EAAAF,EAAAU,KAAAC,MAAAX,EAAA,CAAAY,MAAAC,OAAAN,KAyBTO,WAAaZ,EAAKa,YAAWb,EAE9Cc,QAA+B,KAAId,EACnCe,QAAoC,KAAIf,EAEvBgB,OAAS,SAACC,GACzBjB,EAAKe,QAAUE,EAEM,OAAjBjB,EAAKe,UACPf,EAAKc,QAAU,OAElBd,EAEDkB,iBAAmB,WACjB,IAAAC,EAAuDnB,EAAKoB,MAC5D,OADmBD,EAAXE,YAA0BF,EAAbG,cAA6BC,EAAAA,QAAAJ,EAAAK,IAEnDxB,EAEQyB,kBAAoD,WAC3D,IAAMC,EAAS1B,EAAK2B,YACpB3B,EAAKc,QAAU,IAAIc,EAAY,QAACF,EAAQ1B,EAAKkB,oBAC7ClB,EAAK6B,gBACL7B,EAAK8B,MACN9B,EAEQ+B,qBAA0D,WACjE/B,EAAKgC,OACNhC,EAGQiC,mBAAsD,WAC7DC,OAAOC,OAAOnC,EAAKc,QAASd,EAAKkB,qBAClClB,EAGD2B,UAAY,WACV,GAAqB,OAAjB3B,EAAKe,QACP,MAAMf,EAAKY,WAAWwB,aAExB,OAAOpC,EAAKe,SACbf,EAGDqC,iBAAmB,WAEjB,IAAMX,EAAS1B,EAAK2B,YACdW,EAAOC,SAASC,cAAc,UAMpC,OALAF,EAAKG,MAAQf,EAAOe,MACpBH,EAAKI,OAAShB,EAAOgB,OAErBJ,EAAKK,WAAW,MAAOC,UAAUlB,EAAQ,EAAG,GAErCmB,EAAAA,QAAWP,IACnBtC,EAGD8C,gBAAkB,WAChB,GAAqB,OAAjB9C,EAAKc,QACP,MAAMd,EAAKY,WAAWwB,aAExB,OAAOpC,EAAKc,SACbd,EAED+C,oBAAsB,WACf/C,EAAKoB,MAAME,eAGhBtB,EAAK6B,iBACN7B,EAED6B,cAAgB,WAAY,IAAAmB,EAAAC,EACpB5B,UAAW2B,EAAGhD,EAAKoB,MAAMC,mBAAW,IAAA2B,EAAAA,EAAI,CAAA,EACtCP,EAAkBpB,EAAlBoB,MAAOC,EAAWrB,EAAXqB,OAEf,QAAqB,IAAVD,QAA2C,IAAXC,EAA3C,CAIA,IAAMhB,EAAS1B,EAAK2B,YAIduB,EAAQC,KAAKC,IAA2B,QAAxBH,EAACI,OAAOC,wBAAgBL,IAAAA,EAAAA,EAAI,EAAG,QAEhC,IAAVR,IACTf,EAAOe,MAAQf,EAAO6B,YAAcL,QAEhB,IAAXR,IACThB,EAAOgB,OAAShB,EAAO8B,aAAeN,GAGxCxB,EAAOiB,WAAW,MAAOc,MAAMP,EAAOA,GACtClD,EAAK0D,OAhBL,GAiBD1D,EAEQ2D,OAA8B,WACrC,IAAQtC,EAAgBrB,EAAKoB,MAArBC,YACR,OAAOuC,UAAApB,cAAA,SAAAqB,UAAA,CAAQ5C,IAAKjB,EAAKgB,QAAYK,KACtCrB,EAID8B,GAAyB,WAEvB,OADAuB,OAAOS,iBAAiB,SAAU9D,EAAK+C,qBAChC/C,EAAK8C,kBAAkBhB,MAC/B9B,EAEDgC,IAA2B,WAEzB,OADAqB,OAAOU,oBAAoB,SAAU/D,EAAK+C,qBACnC/C,EAAK8C,kBAAkBd,OAC/BhC,EAED0D,MAA+B,WAC7B,OAAO1D,EAAK8C,kBAAkBY,SAC/B1D,EAEDgE,QAAmC,WACjC,OAAOhE,EAAK8C,kBAAkBkB,WAC/BhE,EAEDiE,YAA2C,SAACC,EAASC,GACnD,OAAOnE,EAAK8C,kBAAkBmB,YAAYC,EAASC,IACpDnE,EAEDoE,UAAuC,SAACC,EAAMC,GAC5C,OAAOtE,EAAK8C,kBAAkBsB,UAAUC,EAAMC,IAC/CtE,EAEDuE,SAAqC,SAACC,GACpC,OAAOxE,EAAK8C,kBAAkByB,SAASC,IACxCxE,EAEDyE,OAAiC,WAC/B,OAAOzE,EAAK8C,kBAAkB2B,UAC/BzE,CAvID,CAsGA,OAAA0E,EAAAA,QAAA/E,EAAA,EA9HmCgF,aAAxBhF,EACKiF,UAAY,CAE1BC,qBAAsBC,EAAS,QAACC,OAChCC,SAAUF,EAAS,QAACC,OACpBE,SAAUH,EAAS,QAACC,OACpBG,YAAaJ,EAAS,QAACC,OACvBI,QAASL,EAAS,QAACM,UAAU,CAACN,EAAS,QAACC,OAAQD,EAAAA,QAAUO,OAC1DC,SAAUR,EAAS,QAACS,OACpBC,SAAUV,EAAS,QAACC,OACpBU,MAAOX,EAAS,QAACO,KACjBK,QAASZ,EAAS,QAACO,KAEnBhE,YAAayD,EAAS,QAACa,OACvBrE,cAAewD,EAAS,QAACc,MAdhBjG,EAiBJkG,aAA4D,CACjEvE,eAAe,GAlBN3B,EAqBJyC,aAAe,IAAI0D,MAAM"}