{"version": 3, "file": "index.mjs", "sources": ["../src/index.tsx"], "sourcesContent": null, "names": ["SignatureCanvas", "_Component", "_inherits", "_super", "_createSuper", "_this", "_classCallCheck", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "staticThis", "constructor", "_sigPad", "_canvas", "setRef", "ref", "_excludeOurProps", "_this$props", "props", "canvasProps", "clearOnResize", "sigPadProps", "_objectWithoutProperties", "_excluded", "componentDidMount", "canvas", "get<PERSON>anvas", "SignaturePad", "_resizeCanvas", "on", "componentWillUnmount", "off", "componentDidUpdate", "Object", "assign", "refNull<PERSON><PERSON>r", "getTrimmedCanvas", "copy", "document", "createElement", "width", "height", "getContext", "drawImage", "trimCanvas", "getSignaturePad", "_checkClearOnResize", "_this$props$canvasPro", "_window$devicePixelRa", "ratio", "Math", "max", "window", "devicePixelRatio", "offsetWidth", "offsetHeight", "scale", "clear", "render", "React", "_extends", "addEventListener", "removeEventListener", "isEmpty", "fromDataURL", "dataURL", "options", "toDataURL", "type", "encoderOptions", "fromData", "pointGroups", "toData", "_createClass", "Component", "propTypes", "velocityFilterWeight", "PropTypes", "number", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minDistance", "dotSize", "oneOfType", "func", "penColor", "string", "throttle", "onEnd", "onBegin", "object", "bool", "defaultProps", "Error"], "mappings": ";;;;;;;;;;;;AAUaA,IAAAA,eAAe,0BAAAC,UAAA,EAAA;EAAAC,SAAA,CAAAF,eAAA,EAAAC,UAAA,CAAA,CAAA;AAAA,EAAA,IAAAE,MAAA,GAAAC,YAAA,CAAAJ,eAAA,CAAA,CAAA;AAAA,EAAA,SAAAA,eAAA,GAAA;AAAA,IAAA,IAAAK,KAAA,CAAA;AAAAC,IAAAA,eAAA,OAAAN,eAAA,CAAA,CAAA;AAAA,IAAA,KAAA,IAAAO,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,GAAAC,IAAAA,KAAA,CAAAJ,IAAA,GAAAK,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA,EAAA,EAAA;AAAAF,MAAAA,IAAA,CAAAE,IAAA,CAAAJ,GAAAA,SAAA,CAAAI,IAAA,CAAA,CAAA;AAAA,KAAA;IAAAP,KAAA,GAAAF,MAAA,CAAAU,IAAA,CAAAC,KAAA,CAAAX,MAAA,EAAA,CAAA,IAAA,CAAA,CAAAY,MAAA,CAAAL,IAAA,CAAA,CAAA,CAAA;AAAAL,IAAAA,KAAA,CAyBTW,UAAU,GAAGX,KAAA,CAAKY,WAAW,CAAA;IAAAZ,KAAA,CAE9Ca,OAAO,GAAwB,IAAI,CAAA;IAAAb,KAAA,CACnCc,OAAO,GAA6B,IAAI,CAAA;AAAAd,IAAAA,KAAA,CAEvBe,MAAM,GAAG,UAACC,GAA6B,EAAW;MACjEhB,KAAA,CAAKc,OAAO,GAAGE,GAAG,CAAA;AAClB;AACA,MAAA,IAAIhB,KAAA,CAAKc,OAAO,KAAK,IAAI,EAAE;QACzBd,KAAA,CAAKa,OAAO,GAAG,IAAI,CAAA;AACrB,OAAA;KACD,CAAA;IAAAb,KAAA,CAEDiB,gBAAgB,GAAG,YAAwC;AACzD,MAAA,IAAAC,WAAA,GAAuDlB,KAAA,CAAKmB,KAAK,CAAA;QAA9CD,WAAA,CAAXE,WAAW,CAAA;QAAeF,WAAA,CAAbG,aAAa,CAAA;AAAKC,YAAAA,WAAW,GAAAC,wBAAA,CAAAL,WAAA,EAAAM,SAAA,EAAA;AAClD,MAAA,OAAOF,WAAW,CAAA;KACnB,CAAA;IAAAtB,KAAA,CAEQyB,iBAAiB,GAAmC,YAAM;AACjE,MAAA,IAAMC,MAAM,GAAG1B,KAAA,CAAK2B,SAAS,EAAE,CAAA;AAC/B3B,MAAAA,KAAA,CAAKa,OAAO,GAAG,IAAIe,YAAY,CAACF,MAAM,EAAE1B,KAAA,CAAKiB,gBAAgB,EAAE,CAAC,CAAA;MAChEjB,KAAA,CAAK6B,aAAa,EAAE,CAAA;MACpB7B,KAAA,CAAK8B,EAAE,EAAE,CAAA;KACV,CAAA;IAAA9B,KAAA,CAEQ+B,oBAAoB,GAAsC,YAAM;MACvE/B,KAAA,CAAKgC,GAAG,EAAE,CAAA;KACX,CAAA;IAAAhC,KAAA,CAGQiC,kBAAkB,GAAoC,YAAM;AACnEC,MAAAA,MAAM,CAACC,MAAM,CAACnC,KAAA,CAAKa,OAAO,EAAEb,KAAA,CAAKiB,gBAAgB,EAAE,CAAC,CAAA;KACrD,CAAA;IAAAjB,KAAA,CAGD2B,SAAS,GAAG,YAAyB;AACnC,MAAA,IAAI3B,KAAA,CAAKc,OAAO,KAAK,IAAI,EAAE;AACzB,QAAA,MAAMd,KAAA,CAAKW,UAAU,CAACyB,YAAY,CAAA;AACpC,OAAA;MACA,OAAOpC,KAAA,CAAKc,OAAO,CAAA;KACpB,CAAA;IAAAd,KAAA,CAGDqC,gBAAgB,GAAG,YAAyB;AAC1C;AACA,MAAA,IAAMX,MAAM,GAAG1B,KAAA,CAAK2B,SAAS,EAAE,CAAA;AAC/B,MAAA,IAAMW,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC,CAAA;AAC7CF,MAAAA,IAAI,CAACG,KAAK,GAAGf,MAAM,CAACe,KAAK,CAAA;AACzBH,MAAAA,IAAI,CAACI,MAAM,GAAGhB,MAAM,CAACgB,MAAM,CAAA;AAC3B;AACAJ,MAAAA,IAAI,CAACK,UAAU,CAAC,IAAI,CAAC,CAAEC,SAAS,CAAClB,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;AAC9C;MACA,OAAOmB,UAAU,CAACP,IAAI,CAAC,CAAA;KACxB,CAAA;IAAAtC,KAAA,CAGD8C,eAAe,GAAG,YAAoB;AACpC,MAAA,IAAI9C,KAAA,CAAKa,OAAO,KAAK,IAAI,EAAE;AACzB,QAAA,MAAMb,KAAA,CAAKW,UAAU,CAACyB,YAAY,CAAA;AACpC,OAAA;MACA,OAAOpC,KAAA,CAAKa,OAAO,CAAA;KACpB,CAAA;IAAAb,KAAA,CAED+C,mBAAmB,GAAG,YAAY;AAChC,MAAA,IAAI,CAAC/C,KAAA,CAAKmB,KAAK,CAACE,aAAa,EAAE;AAAE;AAC/B,QAAA,OAAA;AACF,OAAA;MACArB,KAAA,CAAK6B,aAAa,EAAE,CAAA;KACrB,CAAA;IAAA7B,KAAA,CAED6B,aAAa,GAAG,YAAY;MAAA,IAAAmB,qBAAA,EAAAC,qBAAA,CAAA;AAC1B,MAAA,IAAM7B,WAAW,GAAA,CAAA4B,qBAAA,GAAGhD,KAAA,CAAKmB,KAAK,CAACC,WAAW,cAAA4B,qBAAA,KAAA,KAAA,CAAA,GAAAA,qBAAA,GAAI,EAAE,CAAA;AAChD,MAAA,IAAQP,KAAK,GAAarB,WAAW,CAA7BqB,KAAK;QAAEC,MAAM,GAAKtB,WAAW,CAAtBsB,MAAM,CAAA;AACrB;MACA,IAAI,OAAOD,KAAK,KAAK,WAAW,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;AACjE,QAAA,OAAA;AACF,OAAA;AAEA,MAAA,IAAMhB,MAAM,GAAG1B,KAAA,CAAK2B,SAAS,EAAE,CAAA;AAC/B;AACJ;AACA;AACI,MAAA,IAAMuB,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAA,CAAAH,qBAAA,GAACI,MAAM,CAACC,gBAAgB,MAAA,IAAA,IAAAL,qBAAA,KAAAA,KAAAA,CAAAA,GAAAA,qBAAA,GAAI,CAAC,EAAE,CAAC,CAAC,CAAA;AAEvD,MAAA,IAAI,OAAOR,KAAK,KAAK,WAAW,EAAE;AAChCf,QAAAA,MAAM,CAACe,KAAK,GAAGf,MAAM,CAAC6B,WAAW,GAAGL,KAAK,CAAA;AAC3C,OAAA;AACA,MAAA,IAAI,OAAOR,MAAM,KAAK,WAAW,EAAE;AACjChB,QAAAA,MAAM,CAACgB,MAAM,GAAGhB,MAAM,CAAC8B,YAAY,GAAGN,KAAK,CAAA;AAC7C,OAAA;AACA;MACAxB,MAAM,CAACiB,UAAU,CAAC,IAAI,CAAC,CAAEc,KAAK,CAACP,KAAK,EAAEA,KAAK,CAAC,CAAA;MAC5ClD,KAAA,CAAK0D,KAAK,EAAE,CAAA;KACb,CAAA;IAAA1D,KAAA,CAEQ2D,MAAM,GAAwB,YAAM;AAC3C,MAAA,IAAQvC,WAAW,GAAKpB,KAAA,CAAKmB,KAAK,CAA1BC,WAAW,CAAA;AACnB,MAAA,oBAAOwC,KAAA,CAAApB,aAAA,CAAA,QAAA,EAAAqB,QAAA,CAAA;QAAQ7C,GAAG,EAAEhB,KAAA,CAAKe,MAAAA;OAAYK,EAAAA,WAAW,CAAG,CAAC,CAAA;KACrD,CAAA;IAAApB,KAAA,CAID8B,EAAE,GAAuB,YAAM;MAC7BuB,MAAM,CAACS,gBAAgB,CAAC,QAAQ,EAAE9D,KAAA,CAAK+C,mBAAmB,CAAC,CAAA;MAC3D,OAAO/C,KAAA,CAAK8C,eAAe,EAAE,CAAChB,EAAE,EAAE,CAAA;KACnC,CAAA;IAAA9B,KAAA,CAEDgC,GAAG,GAAwB,YAAM;MAC/BqB,MAAM,CAACU,mBAAmB,CAAC,QAAQ,EAAE/D,KAAA,CAAK+C,mBAAmB,CAAC,CAAA;MAC9D,OAAO/C,KAAA,CAAK8C,eAAe,EAAE,CAACd,GAAG,EAAE,CAAA;KACpC,CAAA;IAAAhC,KAAA,CAED0D,KAAK,GAA0B,YAAM;MACnC,OAAO1D,KAAA,CAAK8C,eAAe,EAAE,CAACY,KAAK,EAAE,CAAA;KACtC,CAAA;IAAA1D,KAAA,CAEDgE,OAAO,GAA4B,YAAM;MACvC,OAAOhE,KAAA,CAAK8C,eAAe,EAAE,CAACkB,OAAO,EAAE,CAAA;KACxC,CAAA;AAAAhE,IAAAA,KAAA,CAEDiE,WAAW,GAAgC,UAACC,OAAO,EAAEC,OAAO,EAAK;MAC/D,OAAOnE,KAAA,CAAK8C,eAAe,EAAE,CAACmB,WAAW,CAACC,OAAO,EAAEC,OAAO,CAAC,CAAA;KAC5D,CAAA;AAAAnE,IAAAA,KAAA,CAEDoE,SAAS,GAA8B,UAACC,IAAI,EAAEC,cAAc,EAAK;MAC/D,OAAOtE,KAAA,CAAK8C,eAAe,EAAE,CAACsB,SAAS,CAACC,IAAI,EAAEC,cAAc,CAAC,CAAA;KAC9D,CAAA;AAAAtE,IAAAA,KAAA,CAEDuE,QAAQ,GAA6B,UAACC,WAAW,EAAK;MACpD,OAAOxE,KAAA,CAAK8C,eAAe,EAAE,CAACyB,QAAQ,CAACC,WAAW,CAAC,CAAA;KACpD,CAAA;IAAAxE,KAAA,CAEDyE,MAAM,GAA2B,YAAM;MACrC,OAAOzE,KAAA,CAAK8C,eAAe,EAAE,CAAC2B,MAAM,EAAE,CAAA;KACvC,CAAA;AAAA,IAAA,OAAAzE,KAAA,CAAA;GAvID;AA8BA;AAKA;AAQA;AAaA;AA6CA;AACA;EAAA,OAAA0E,YAAA,CAAA/E,eAAA,CAAA,CAAA;AAAA,CAAA,CA9HmCgF,SAAS,EAAA;AAAjChF,eAAe,CACViF,SAAS,GAAG;AAC1B;EACAC,oBAAoB,EAAEC,SAAS,CAACC,MAAM;EACtCC,QAAQ,EAAEF,SAAS,CAACC,MAAM;EAC1BE,QAAQ,EAAEH,SAAS,CAACC,MAAM;EAC1BG,WAAW,EAAEJ,SAAS,CAACC,MAAM;AAC7BI,EAAAA,OAAO,EAAEL,SAAS,CAACM,SAAS,CAAC,CAACN,SAAS,CAACC,MAAM,EAAED,SAAS,CAACO,IAAI,CAAC,CAAC;EAChEC,QAAQ,EAAER,SAAS,CAACS,MAAM;EAC1BC,QAAQ,EAAEV,SAAS,CAACC,MAAM;EAC1BU,KAAK,EAAEX,SAAS,CAACO,IAAI;EACrBK,OAAO,EAAEZ,SAAS,CAACO,IAAI;AACvB;EACAjE,WAAW,EAAE0D,SAAS,CAACa,MAAM;EAC7BtE,aAAa,EAAEyD,SAAS,CAACc,IAAAA;AAC3B,CAAC,CAAA;AAfUjG,eAAe,CAiBnBkG,YAAY,GAAgD;AACjExE,EAAAA,aAAa,EAAE,IAAA;AACjB,CAAC,CAAA;AAnBU1B,eAAe,CAqBnByC,YAAY,GAAG,IAAI0D,KAAK,CAAC,sCAAsC,GACpE,gEAAgE,CAAC;;;;"}